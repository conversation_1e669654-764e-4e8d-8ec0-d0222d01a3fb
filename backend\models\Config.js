const mongoose = require('mongoose');

const configSchema = new mongoose.Schema({
  // 阿里云配置
  aliyun: {
    accessKeyId: {
      type: String,
      required: true
    },
    accessKeySecret: {
      type: String,
      required: true
    },
    region: {
      type: String,
      default: 'cn-hangzhou'
    },
    oss: {
      bucket: String,
      region: String,
      endpoint: String
    }
  },
  
  // 腾讯云配置
  tencent: {
    secretId: String,
    secretKey: String,
    cos: {
      bucket: String,
      region: String
    }
  },
  
  // AI服务配置
  aiServices: {
    idCardRecognition: {
      enabled: {
        type: Boolean,
        default: true
      },
      endpoint: String
    },
    portraitEnhancement: {
      enabled: {
        type: Boolean,
        default: true
      },
      endpoint: String
    },
    imageColorization: {
      enabled: {
        type: Boolean,
        default: true
      },
      endpoint: String
    },
    imageUpscaling: {
      enabled: {
        type: Boolean,
        default: true
      },
      endpoint: String
    },
    imageTranslation: {
      enabled: {
        type: Boolean,
        default: true
      },
      endpoint: String
    }
  },
  
  // 系统配置
  system: {
    maxFileSize: {
      type: Number,
      default: 10485760 // 10MB
    },
    allowedFileTypes: {
      type: [String],
      default: ['image/jpeg', 'image/png', 'image/jpg']
    },
    storageProvider: {
      type: String,
      enum: ['aliyun', 'tencent', 'both'],
      default: 'aliyun'
    }
  }
}, {
  timestamps: true
});

// 确保只有一个配置文档
configSchema.statics.getSingleton = async function() {
  let config = await this.findOne();
  if (!config) {
    config = await this.create({});
  }
  return config;
};

module.exports = mongoose.model('Config', configSchema);

const express = require('express');
const router = express.Router();

// 预定义的证件照尺寸数据
const PHOTO_SIZES = [
  {
    id: 1,
    name: '一寸照片',
    widthMm: 25,
    heightMm: 35,
    widthPx: 295,
    heightPx: 413,
    category: 1,
    description: '常用于身份证、学生证等',
    dpi: 300
  },
  {
    id: 2,
    name: '二寸照片',
    widthMm: 35,
    heightMm: 49,
    widthPx: 413,
    heightPx: 579,
    category: 1,
    description: '常用于护照、简历等',
    dpi: 300
  },
  {
    id: 3,
    name: '驾驶证照片',
    widthMm: 22,
    heightMm: 32,
    widthPx: 260,
    heightPx: 378,
    category: 2,
    description: '驾驶证专用尺寸',
    dpi: 300
  },
  {
    id: 4,
    name: '护照照片',
    widthMm: 33,
    heightMm: 48,
    widthPx: 390,
    heightPx: 567,
    category: 3,
    description: '护照专用尺寸',
    dpi: 300
  },
  {
    id: 5,
    name: '港澳通行证',
    widthMm: 33,
    heightMm: 48,
    widthPx: 390,
    heightPx: 567,
    category: 3,
    description: '港澳通行证专用尺寸',
    dpi: 300
  },
  {
    id: 6,
    name: '社保照片',
    widthMm: 25,
    heightMm: 35,
    widthPx: 295,
    heightPx: 413,
    category: 4,
    description: '社保卡专用尺寸',
    dpi: 300
  }
];

// 获取证件照尺寸列表
router.get('/list', async (req, res) => {
  try {
    const { category, page = 1, limit = 20 } = req.query;
    
    let filteredSizes = PHOTO_SIZES;
    
    // 按分类筛选
    if (category) {
      filteredSizes = PHOTO_SIZES.filter(size => size.category == category);
    }
    
    // 分页处理
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedSizes = filteredSizes.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        records: paginatedSizes,
        total: filteredSizes.length,
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(filteredSizes.length / limit)
      },
      message: '获取尺寸列表成功'
    });

  } catch (error) {
    console.error('获取尺寸列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取尺寸列表失败',
      message: error.message
    });
  }
});

// 搜索证件照尺寸
router.get('/search', async (req, res) => {
  try {
    const { name = '', pageNum = 1, pageSize = 20, type } = req.query;
    
    let filteredSizes = PHOTO_SIZES;
    
    // 按名称搜索
    if (name) {
      filteredSizes = PHOTO_SIZES.filter(size => 
        size.name.includes(name) || size.description.includes(name)
      );
    }
    
    // 按类型筛选
    if (type !== undefined) {
      filteredSizes = filteredSizes.filter(size => size.category == type);
    }
    
    // 分页处理
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + parseInt(pageSize);
    const paginatedSizes = filteredSizes.slice(startIndex, endIndex);
    
    res.json({
      success: true,
      data: {
        records: paginatedSizes,
        total: filteredSizes.length,
        size: parseInt(pageSize),
        current: parseInt(pageNum),
        pages: Math.ceil(filteredSizes.length / pageSize)
      },
      message: '搜索成功'
    });

  } catch (error) {
    console.error('搜索失败:', error);
    res.status(500).json({
      success: false,
      error: '搜索失败',
      message: error.message
    });
  }
});

// 获取单个尺寸详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const size = PHOTO_SIZES.find(s => s.id == id);
    
    if (!size) {
      return res.status(404).json({
        success: false,
        error: '尺寸不存在'
      });
    }
    
    res.json({
      success: true,
      data: size,
      message: '获取尺寸详情成功'
    });

  } catch (error) {
    console.error('获取尺寸详情失败:', error);
    res.status(500).json({
      success: false,
      error: '获取尺寸详情失败',
      message: error.message
    });
  }
});

module.exports = router;

const express = require('express');
const router = express.Router();
const Config = require('../models/Config');

// 获取配置
router.get('/', async (req, res) => {
  try {
    console.log('开始获取配置...');

    // 临时返回默认配置，避免数据库问题
    const defaultConfig = {
      aliyun: {
        accessKeyId: '',
        accessKeySecret: '',
        region: 'cn-hangzhou',
        oss: {
          bucket: '',
          region: 'oss-cn-hangzhou',
          endpoint: 'https://oss-cn-hangzhou.aliyuncs.com'
        }
      },
      tencent: {
        secretId: '',
        secretKey: '',
        cos: {
          bucket: '',
          region: 'ap-beijing'
        }
      },
      aiServices: {
        idPhotoCreation: {
          enabled: true,
          endpoint: 'https://imageseg.cn-shanghai.aliyuncs.com'
        },
        portraitEnhancement: {
          enabled: true,
          endpoint: 'https://imagerecog.cn-shanghai.aliyuncs.com'
        },
        imageColorization: {
          enabled: true,
          endpoint: 'https://imagerecog.cn-shanghai.aliyuncs.com'
        },
        imageUpscaling: {
          enabled: true,
          endpoint: 'https://imagerecog.cn-shanghai.aliyuncs.com'
        },
        imageTranslation: {
          enabled: true,
          endpoint: 'https://imagerecog.cn-shanghai.aliyuncs.com'
        },
        portraitClarification: {
          enabled: true,
          endpoint: 'https://imagerecog.cn-shanghai.aliyuncs.com'
        }
      },
      system: {
        maxFileSize: 10485760,
        allowedFileTypes: ['image/jpeg', 'image/png', 'image/jpg'],
        storageProvider: 'aliyun'
      }
    };

    console.log('返回默认配置');

    res.json({
      success: true,
      data: defaultConfig
    });
  } catch (error) {
    console.error('获取配置失败:', error);
    res.status(500).json({
      success: false,
      error: '获取配置失败',
      message: error.message
    });
  }
});

// 更新阿里云配置
router.put('/aliyun', async (req, res) => {
  try {
    const { accessKeyId, accessKeySecret, region, oss } = req.body;
    
    const config = await Config.getSingleton();
    config.aliyun = {
      accessKeyId,
      accessKeySecret,
      region: region || 'cn-hangzhou',
      oss: oss || config.aliyun.oss
    };
    
    await config.save();
    
    res.json({
      success: true,
      message: '阿里云配置更新成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '更新阿里云配置失败',
      message: error.message
    });
  }
});

// 更新腾讯云配置
router.put('/tencent', async (req, res) => {
  try {
    const { secretId, secretKey, cos } = req.body;
    
    const config = await Config.getSingleton();
    config.tencent = {
      secretId,
      secretKey,
      cos: cos || config.tencent.cos
    };
    
    await config.save();
    
    res.json({
      success: true,
      message: '腾讯云配置更新成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '更新腾讯云配置失败',
      message: error.message
    });
  }
});

// 更新AI服务配置
router.put('/ai-services', async (req, res) => {
  try {
    const { aiServices } = req.body;
    
    const config = await Config.getSingleton();
    config.aiServices = {
      ...config.aiServices,
      ...aiServices
    };
    
    await config.save();
    
    res.json({
      success: true,
      message: 'AI服务配置更新成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '更新AI服务配置失败',
      message: error.message
    });
  }
});

// 更新系统配置
router.put('/system', async (req, res) => {
  try {
    const { system } = req.body;
    
    const config = await Config.getSingleton();
    config.system = {
      ...config.system,
      ...system
    };
    
    await config.save();
    
    res.json({
      success: true,
      message: '系统配置更新成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '更新系统配置失败',
      message: error.message
    });
  }
});

// 测试配置连接
router.post('/test', async (req, res) => {
  try {
    const { provider } = req.body;
    const config = await Config.getSingleton();
    
    let testResult = { success: false, message: '' };
    
    if (provider === 'aliyun') {
      // 测试阿里云连接
      const { testAliyunConnection } = require('../services/aliyunService');
      testResult = await testAliyunConnection(config.aliyun);
    } else if (provider === 'tencent') {
      // 测试腾讯云连接
      const { testTencentConnection } = require('../services/tencentService');
      testResult = await testTencentConnection(config.tencent);
    }
    
    res.json(testResult);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: '测试连接失败',
      message: error.message
    });
  }
});

// 获取美颜设置
router.get('/beauty-settings', async (req, res) => {
  try {
    const config = await Config.getSingleton();

    res.json({
      success: true,
      data: {
        enabled: config.features?.beauty?.enabled || true,
        level: config.features?.beauty?.level || 'medium',
        options: ['light', 'medium', 'strong']
      },
      message: '获取美颜设置成功'
    });

  } catch (error) {
    console.error('获取美颜设置失败:', error);
    res.status(500).json({
      success: false,
      error: '获取美颜设置失败',
      message: error.message
    });
  }
});

// 更新美颜设置
router.put('/beauty-settings', async (req, res) => {
  try {
    const { enabled, level } = req.body;

    const config = await Config.getSingleton();

    if (!config.features) config.features = {};
    if (!config.features.beauty) config.features.beauty = {};

    if (enabled !== undefined) config.features.beauty.enabled = enabled;
    if (level !== undefined) config.features.beauty.level = level;

    await config.save();

    res.json({
      success: true,
      data: config.features.beauty,
      message: '美颜设置更新成功'
    });

  } catch (error) {
    console.error('更新美颜设置失败:', error);
    res.status(500).json({
      success: false,
      error: '更新美颜设置失败',
      message: error.message
    });
  }
});

// 获取广告配置
router.get('/advertisement', async (req, res) => {
  try {
    const config = await Config.getSingleton();

    res.json({
      success: true,
      data: {
        enabled: config.features?.advertisement?.enabled || false,
        videoUnitId: config.features?.advertisement?.videoUnitId || '',
        bannerUnitId: config.features?.advertisement?.bannerUnitId || '',
        interstitialUnitId: config.features?.advertisement?.interstitialUnitId || '',
        rewardedVideoEnabled: config.features?.advertisement?.rewardedVideoEnabled || false
      },
      message: '获取广告配置成功'
    });

  } catch (error) {
    console.error('获取广告配置失败:', error);
    res.status(500).json({
      success: false,
      error: '获取广告配置失败',
      message: error.message
    });
  }
});

// 更新广告配置
router.put('/advertisement', async (req, res) => {
  try {
    const {
      enabled,
      videoUnitId,
      bannerUnitId,
      interstitialUnitId,
      rewardedVideoEnabled
    } = req.body;

    const config = await Config.getSingleton();

    if (!config.features) config.features = {};
    if (!config.features.advertisement) config.features.advertisement = {};

    const adConfig = config.features.advertisement;
    if (enabled !== undefined) adConfig.enabled = enabled;
    if (videoUnitId !== undefined) adConfig.videoUnitId = videoUnitId;
    if (bannerUnitId !== undefined) adConfig.bannerUnitId = bannerUnitId;
    if (interstitialUnitId !== undefined) adConfig.interstitialUnitId = interstitialUnitId;
    if (rewardedVideoEnabled !== undefined) adConfig.rewardedVideoEnabled = rewardedVideoEnabled;

    await config.save();

    res.json({
      success: true,
      data: config.features.advertisement,
      message: '广告配置更新成功'
    });

  } catch (error) {
    console.error('更新广告配置失败:', error);
    res.status(500).json({
      success: false,
      error: '更新广告配置失败',
      message: error.message
    });
  }
});

module.exports = router;

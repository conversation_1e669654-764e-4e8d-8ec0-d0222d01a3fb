const express = require('express');
const router = express.Router();
const UserWork = require('../models/UserWork');
const ProcessRecord = require('../models/ProcessRecord');

// 获取用户作品列表
router.get('/list', async (req, res) => {
  try {
    const { 
      userId, 
      type, 
      isFavorite, 
      pageNum = 1, 
      pageSize = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        error: '请提供用户ID'
      });
    }

    // 构建查询条件
    const query = { userId };
    if (type) query.type = type;
    if (isFavorite !== undefined) query.isFavorite = isFavorite === 'true';

    // 构建排序条件
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // 分页查询
    const skip = (pageNum - 1) * pageSize;
    const limit = parseInt(pageSize);

    const [works, total] = await Promise.all([
      UserWork.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .populate('processRecordId', 'timing result')
        .lean(),
      UserWork.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        records: works,
        total,
        pageNum: parseInt(pageNum),
        pageSize: parseInt(pageSize),
        pages: Math.ceil(total / pageSize)
      },
      message: '获取作品列表成功'
    });

  } catch (error) {
    console.error('获取作品列表失败:', error);
    res.status(500).json({
      success: false,
      error: '获取作品列表失败',
      message: error.message
    });
  }
});

// 保存用户作品
router.post('/save', async (req, res) => {
  try {
    const {
      userId,
      name,
      type,
      originalImage,
      processedImage,
      parameters = {},
      processRecordId,
      tags = []
    } = req.body;

    if (!userId || !name || !type || !processedImage?.url) {
      return res.status(400).json({
        success: false,
        error: '请提供必要的作品信息'
      });
    }

    const userWork = new UserWork({
      userId,
      name,
      type,
      originalImage,
      processedImage,
      parameters,
      processRecordId,
      tags,
      status: 'completed'
    });

    await userWork.save();

    res.json({
      success: true,
      data: userWork,
      message: '作品保存成功'
    });

  } catch (error) {
    console.error('保存作品失败:', error);
    res.status(500).json({
      success: false,
      error: '保存作品失败',
      message: error.message
    });
  }
});

// 获取单个作品详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.query;

    const work = await UserWork.findById(id)
      .populate('processRecordId')
      .lean();

    if (!work) {
      return res.status(404).json({
        success: false,
        error: '作品不存在'
      });
    }

    // 检查权限（只有作品所有者或公开作品可以查看）
    if (work.userId !== userId && !work.isPublic) {
      return res.status(403).json({
        success: false,
        error: '无权访问此作品'
      });
    }

    res.json({
      success: true,
      data: work,
      message: '获取作品详情成功'
    });

  } catch (error) {
    console.error('获取作品详情失败:', error);
    res.status(500).json({
      success: false,
      error: '获取作品详情失败',
      message: error.message
    });
  }
});

// 更新作品信息
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, name, tags, isFavorite, isPublic } = req.body;

    const work = await UserWork.findById(id);

    if (!work) {
      return res.status(404).json({
        success: false,
        error: '作品不存在'
      });
    }

    // 检查权限
    if (work.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: '无权修改此作品'
      });
    }

    // 更新字段
    if (name !== undefined) work.name = name;
    if (tags !== undefined) work.tags = tags;
    if (isFavorite !== undefined) work.isFavorite = isFavorite;
    if (isPublic !== undefined) work.isPublic = isPublic;

    await work.save();

    res.json({
      success: true,
      data: work,
      message: '作品更新成功'
    });

  } catch (error) {
    console.error('更新作品失败:', error);
    res.status(500).json({
      success: false,
      error: '更新作品失败',
      message: error.message
    });
  }
});

// 删除作品
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.query;

    const work = await UserWork.findById(id);

    if (!work) {
      return res.status(404).json({
        success: false,
        error: '作品不存在'
      });
    }

    // 检查权限
    if (work.userId !== userId) {
      return res.status(403).json({
        success: false,
        error: '无权删除此作品'
      });
    }

    await UserWork.findByIdAndDelete(id);

    res.json({
      success: true,
      message: '作品删除成功'
    });

  } catch (error) {
    console.error('删除作品失败:', error);
    res.status(500).json({
      success: false,
      error: '删除作品失败',
      message: error.message
    });
  }
});

// 下载作品
router.post('/:id/download', async (req, res) => {
  try {
    const { id } = req.params;

    const work = await UserWork.findById(id);

    if (!work) {
      return res.status(404).json({
        success: false,
        error: '作品不存在'
      });
    }

    // 增加下载次数
    await work.incrementDownload();

    res.json({
      success: true,
      data: {
        downloadUrl: work.processedImage.url,
        filename: work.processedImage.filename || `${work.name}.jpg`
      },
      message: '获取下载链接成功'
    });

  } catch (error) {
    console.error('下载作品失败:', error);
    res.status(500).json({
      success: false,
      error: '下载作品失败',
      message: error.message
    });
  }
});

// 分享作品
router.post('/:id/share', async (req, res) => {
  try {
    const { id } = req.params;

    const work = await UserWork.findById(id);

    if (!work) {
      return res.status(404).json({
        success: false,
        error: '作品不存在'
      });
    }

    // 增加分享次数
    await work.incrementShare();

    res.json({
      success: true,
      data: {
        shareUrl: `${req.protocol}://${req.get('host')}/api/user-works/${id}`,
        title: work.name,
        description: `查看我制作的${work.type === 'idPhoto' ? '证件照' : '图片处理'}作品`
      },
      message: '获取分享信息成功'
    });

  } catch (error) {
    console.error('分享作品失败:', error);
    res.status(500).json({
      success: false,
      error: '分享作品失败',
      message: error.message
    });
  }
});

// 获取用户作品统计
router.get('/stats/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    const stats = await UserWork.getUserStats(userId);
    
    // 计算总数
    const totalWorks = stats.reduce((sum, stat) => sum + stat.count, 0);
    const totalDownloads = stats.reduce((sum, stat) => sum + stat.totalDownloads, 0);
    const totalShares = stats.reduce((sum, stat) => sum + stat.totalShares, 0);

    res.json({
      success: true,
      data: {
        totalWorks,
        totalDownloads,
        totalShares,
        byType: stats
      },
      message: '获取统计信息成功'
    });

  } catch (error) {
    console.error('获取统计信息失败:', error);
    res.status(500).json({
      success: false,
      error: '获取统计信息失败',
      message: error.message
    });
  }
});

module.exports = router;

const mongoose = require('mongoose');

const processRecordSchema = new mongoose.Schema({
  // 用户信息
  userId: {
    type: String,
    required: true
  },
  
  // 处理类型
  processType: {
    type: String,
    required: true,
    enum: [
      'idPhotoCreation',
      'portraitEnhancement',
      'imageColorization',
      'imageUpscaling',
      'imageTranslation',
      'portraitClarification'
    ]
  },
  
  // 原始文件信息
  originalFile: {
    filename: String,
    originalName: String,
    size: Number,
    mimetype: String,
    url: String,
    storageProvider: String
  },
  
  // 处理后文件信息
  processedFile: {
    filename: String,
    size: Number,
    url: String,
    storageProvider: String
  },
  
  // 处理状态
  status: {
    type: String,
    enum: ['pending', 'processing', 'completed', 'failed'],
    default: 'pending'
  },
  
  // 处理结果
  result: {
    success: Boolean,
    data: mongoose.Schema.Types.Mixed,
    error: String
  },
  
  // 处理时间统计
  timing: {
    startTime: Date,
    endTime: Date,
    duration: Number // 毫秒
  },
  
  // API调用信息
  apiCall: {
    provider: String,
    endpoint: String,
    requestId: String,
    cost: Number
  }
}, {
  timestamps: true
});

// 索引
processRecordSchema.index({ userId: 1, createdAt: -1 });
processRecordSchema.index({ processType: 1, createdAt: -1 });
processRecordSchema.index({ status: 1 });

module.exports = mongoose.model('ProcessRecord', processRecordSchema);

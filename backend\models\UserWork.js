const mongoose = require('mongoose');

const userWorkSchema = new mongoose.Schema({
  // 用户ID
  userId: {
    type: String,
    required: true,
    index: true
  },
  
  // 作品名称
  name: {
    type: String,
    required: true,
    trim: true
  },
  
  // 作品类型
  type: {
    type: String,
    enum: ['idPhoto', 'enhanced', 'colorized', 'upscaled', 'translated'],
    required: true
  },
  
  // 原始图片信息
  originalImage: {
    url: {
      type: String,
      required: true
    },
    filename: String,
    size: Number,
    mimetype: String
  },
  
  // 处理后图片信息
  processedImage: {
    url: {
      type: String,
      required: true
    },
    filename: String,
    size: Number,
    mimetype: String
  },
  
  // 处理参数
  parameters: {
    // 证件照尺寸信息
    photoSize: {
      id: Number,
      name: String,
      widthMm: Number,
      heightMm: Number,
      widthPx: Number,
      heightPx: Number
    },
    
    // 背景颜色
    backgroundColor: String,
    
    // 放大倍数
    scale: Number,
    
    // 翻译语言
    targetLanguage: String,
    
    // 其他参数
    dpi: Number,
    quality: Number
  },
  
  // 处理状态
  status: {
    type: String,
    enum: ['processing', 'completed', 'failed'],
    default: 'completed'
  },
  
  // 处理记录ID
  processRecordId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ProcessRecord'
  },
  
  // 标签
  tags: [String],
  
  // 是否收藏
  isFavorite: {
    type: Boolean,
    default: false
  },
  
  // 是否公开
  isPublic: {
    type: Boolean,
    default: false
  },
  
  // 下载次数
  downloadCount: {
    type: Number,
    default: 0
  },
  
  // 分享次数
  shareCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// 索引
userWorkSchema.index({ userId: 1, createdAt: -1 });
userWorkSchema.index({ type: 1, createdAt: -1 });
userWorkSchema.index({ isFavorite: 1, userId: 1 });

// 静态方法：获取用户作品统计
userWorkSchema.statics.getUserStats = async function(userId) {
  const stats = await this.aggregate([
    { $match: { userId } },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
        totalDownloads: { $sum: '$downloadCount' },
        totalShares: { $sum: '$shareCount' }
      }
    }
  ]);
  
  return stats;
};

// 实例方法：增加下载次数
userWorkSchema.methods.incrementDownload = async function() {
  this.downloadCount += 1;
  return this.save();
};

// 实例方法：增加分享次数
userWorkSchema.methods.incrementShare = async function() {
  this.shareCount += 1;
  return this.save();
};

module.exports = mongoose.model('UserWork', userWorkSchema);

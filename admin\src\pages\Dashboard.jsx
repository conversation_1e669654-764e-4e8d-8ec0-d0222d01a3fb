import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Statistic, 
  Typography, 
  Alert,
  Spin,
  Tag,
  Space,
  Button
} from 'antd';
import { 
  CloudOutlined,
  FileImageOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { systemAPI, aiAPI, configAPI } from '../services/api';

const { Title, Paragraph } = Typography;

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [systemStatus, setSystemStatus] = useState(null);
  const [statistics, setStatistics] = useState({
    totalProcessed: 0,
    todayProcessed: 0,
    successRate: 0,
    failedCount: 0
  });
  const [configStatus, setConfigStatus] = useState({
    aliyun: false,
    tencent: false
  });

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // 检查系统健康状态
      const healthResponse = await systemAPI.healthCheck();
      setSystemStatus(healthResponse);

      // 获取处理统计
      const recordsResponse = await aiAPI.getRecords({ limit: 1000 });
      if (recordsResponse.success) {
        const records = recordsResponse.data.records;
        const today = new Date().toDateString();
        const todayRecords = records.filter(record => 
          new Date(record.createdAt).toDateString() === today
        );
        const successRecords = records.filter(record => record.status === 'completed');
        
        setStatistics({
          totalProcessed: records.length,
          todayProcessed: todayRecords.length,
          successRate: records.length > 0 ? Math.round((successRecords.length / records.length) * 100) : 0,
          failedCount: records.filter(record => record.status === 'failed').length
        });
      }

      // 检查配置状态
      const configResponse = await configAPI.getConfig();
      if (configResponse.success) {
        const config = configResponse.data;
        setConfigStatus({
          aliyun: !!(config.aliyun?.accessKeyId && config.aliyun?.accessKeySecret),
          tencent: !!(config.tencent?.secretId && config.tencent?.secretKey)
        });
      }

    } catch (error) {
      console.error('加载仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, []);

  const getStatusColor = (status) => {
    return status ? 'success' : 'error';
  };

  const getStatusText = (status) => {
    return status ? '已配置' : '未配置';
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载中...</div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Space align="center" style={{ width: '100%', justifyContent: 'space-between' }}>
          <div>
            <Title level={2} style={{ margin: 0 }}>
              系统概览
            </Title>
            <Paragraph type="secondary">
              AI图像处理系统运行状态和统计信息
            </Paragraph>
          </div>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={loadDashboardData}
            loading={loading}
          >
            刷新数据
          </Button>
        </Space>
      </div>

      {/* 系统状态 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Alert
            message={
              <Space>
                <span>系统状态:</span>
                <Tag color={systemStatus ? 'green' : 'red'}>
                  {systemStatus ? '运行正常' : '连接失败'}
                </Tag>
                {systemStatus && (
                  <span style={{ color: '#666' }}>
                    最后更新: {new Date(systemStatus.timestamp).toLocaleString()}
                  </span>
                )}
              </Space>
            }
            type={systemStatus ? 'success' : 'error'}
            showIcon
          />
        </Col>
      </Row>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总处理量"
              value={statistics.totalProcessed}
              prefix={<FileImageOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日处理"
              value={statistics.todayProcessed}
              prefix={<CloudOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="成功率"
              value={statistics.successRate}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="失败次数"
              value={statistics.failedCount}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 配置状态 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="云服务配置状态" extra={<CloudOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>阿里云服务:</span>
                <Tag color={getStatusColor(configStatus.aliyun)}>
                  {getStatusText(configStatus.aliyun)}
                </Tag>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>腾讯云服务:</span>
                <Tag color={getStatusColor(configStatus.tencent)}>
                  {getStatusText(configStatus.tencent)}
                </Tag>
              </div>
            </Space>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="快速操作" extra={<FileImageOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button type="primary" block href="/config">
                配置云服务
              </Button>
              <Button block href="/records">
                查看处理记录
              </Button>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;

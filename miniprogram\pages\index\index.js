// pages/index/index.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    searchValue: '',
    bannerList: [
      {
        id: 1,
        title: 'AI智能证件照制作',
        description: '一键生成专业证件照，支持多种尺寸规格',
        imageUrl: '/images/banner1.jpg',
        link: '/pages/idphoto/idphoto'
      },
      {
        id: 2,
        title: '人像智能增强',
        description: '提升照片清晰度，让您的照片更加专业',
        imageUrl: '/images/banner2.jpg',
        link: '/pages/enhance/enhance'
      },
      {
        id: 3,
        title: '背景一键替换',
        description: '红蓝白背景随心换，符合各种证件要求',
        imageUrl: '/images/banner3.jpg',
        link: '/pages/background/background'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('首页加载完成')
    this.loadBannerData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('首页渲染完成')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('首页显示')
  },

  /**
   * 搜索输入事件
   */
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    })
  },

  /**
   * 搜索事件
   */
  onSearch() {
    const searchValue = this.data.searchValue.trim()
    if (!searchValue) {
      wx.showToast({
        title: '请输入搜索内容',
        icon: 'none'
      })
      return
    }
    
    console.log('搜索内容:', searchValue)
    wx.showToast({
      title: `搜索: ${searchValue}`,
      icon: 'none'
    })
    
    // TODO: 实现搜索功能
    // 可以跳转到搜索结果页面或者在当前页面显示搜索结果
  },

  /**
   * 轮播图点击事件
   */
  onBannerTap(e) {
    const item = e.currentTarget.dataset.item
    console.log('点击轮播图:', item)
    
    wx.showToast({
      title: `点击了: ${item.title}`,
      icon: 'none'
    })
    
    // TODO: 根据轮播图配置跳转到对应页面
    // if (item.link) {
    //   wx.navigateTo({
    //     url: item.link
    //   })
    // }
  },

  /**
   * 加载轮播图数据
   */
  loadBannerData() {
    // 这里可以从后端API获取轮播图数据
    // 目前使用静态数据
    console.log('轮播图数据加载完成')
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新')
    // 重新加载数据
    this.loadBannerData()
    
    // 停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('上拉触底')
    // TODO: 加载更多数据
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'AI证件照制作小程序',
      path: '/pages/index/index',
      imageUrl: '/images/share.jpg'
    }
  }
})

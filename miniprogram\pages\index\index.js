// pages/index/index.js
const app = getApp()

Page({
  /**
   * 页面的初始数据
   */
  data: {
    statusBarHeight: 0,
    searchValue: '',
    bannerList: [
      {
        id: 1,
        badge: 'AI智能',
        title: '专业证件照制作',
        description: '一键生成标准证件照，支持多种尺寸规格，AI智能抠图',
        imageUrl: '/images/banner1.jpg',
        link: '/pages/idphoto/idphoto'
      },
      {
        id: 2,
        badge: '高清修复',
        title: '人像智能增强',
        description: 'AI算法提升照片清晰度，让您的照片更加专业自然',
        imageUrl: '/images/banner2.jpg',
        link: '/pages/enhance/enhance'
      },
      {
        id: 3,
        badge: '背景替换',
        title: '一键换背景',
        description: '红蓝白背景随心换，符合各种证件照标准要求',
        imageUrl: '/images/banner3.jpg',
        link: '/pages/background/background'
      }
    ],
    quickActions: [
      {
        id: 1,
        icon: '📷',
        title: '证件照',
        desc: '制作证件照',
        bgColor: 'linear-gradient(135deg, #667eea, #764ba2)',
        action: 'idphoto'
      },
      {
        id: 2,
        icon: '✨',
        title: '人像增强',
        desc: '提升清晰度',
        bgColor: 'linear-gradient(135deg, #f093fb, #f5576c)',
        action: 'enhance'
      },
      {
        id: 3,
        icon: '🎨',
        title: '背景替换',
        desc: '更换背景色',
        bgColor: 'linear-gradient(135deg, #4facfe, #00f2fe)',
        action: 'background'
      },
      {
        id: 4,
        icon: '📋',
        title: '历史记录',
        desc: '查看记录',
        bgColor: 'linear-gradient(135deg, #43e97b, #38f9d7)',
        action: 'history'
      }
    ],
    featuredServices: [
      {
        id: 1,
        title: '专业证件照制作',
        description: 'AI智能抠图，一键生成标准证件照，支持一寸、二寸、驾驶证等多种规格',
        imageUrl: '/images/service1.jpg',
        tag: '热门',
        features: ['AI抠图', '多种尺寸', '标准背景', '高清输出'],
        price: '免费体验',
        action: 'idphoto'
      },
      {
        id: 2,
        title: '人像智能修复',
        description: '采用先进AI算法，智能修复模糊照片，提升人像清晰度和质量',
        imageUrl: '/images/service2.jpg',
        tag: '推荐',
        features: ['智能修复', '清晰度提升', '噪点去除', '细节增强'],
        price: '限时免费',
        action: 'enhance'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('首页加载完成')
    this.initPage()
    this.loadBannerData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    console.log('首页渲染完成')
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('首页显示')
  },

  /**
   * 初始化页面
   */
  initPage() {
    // 获取系统信息
    const systemInfo = wx.getSystemInfoSync()
    this.setData({
      statusBarHeight: systemInfo.statusBarHeight
    })
  },

  /**
   * 搜索输入事件
   */
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    })
  },

  /**
   * 搜索事件
   */
  onSearch() {
    const searchValue = this.data.searchValue.trim()
    if (!searchValue) {
      wx.showToast({
        title: '请输入搜索内容',
        icon: 'none'
      })
      return
    }
    
    console.log('搜索内容:', searchValue)
    wx.showToast({
      title: `搜索: ${searchValue}`,
      icon: 'none'
    })
    
    // TODO: 实现搜索功能
    // 可以跳转到搜索结果页面或者在当前页面显示搜索结果
  },

  /**
   * 轮播图点击事件
   */
  onBannerTap(e) {
    const item = e.currentTarget.dataset.item
    console.log('点击轮播图:', item)

    wx.showToast({
      title: `点击了: ${item.title}`,
      icon: 'none'
    })

    // TODO: 根据轮播图配置跳转到对应页面
    // if (item.link) {
    //   wx.navigateTo({
    //     url: item.link
    //   })
    // }
  },

  /**
   * 快捷功能点击事件
   */
  onActionTap(e) {
    const action = e.currentTarget.dataset.action
    console.log('点击快捷功能:', action)

    switch (action.action) {
      case 'idphoto':
        wx.showToast({
          title: '证件照制作功能开发中',
          icon: 'none'
        })
        break
      case 'enhance':
        wx.showToast({
          title: '人像增强功能开发中',
          icon: 'none'
        })
        break
      case 'background':
        wx.showToast({
          title: '背景替换功能开发中',
          icon: 'none'
        })
        break
      case 'history':
        wx.showToast({
          title: '历史记录功能开发中',
          icon: 'none'
        })
        break
      default:
        break
    }
  },

  /**
   * 特色服务点击事件
   */
  onServiceTap(e) {
    const service = e.currentTarget.dataset.service
    console.log('点击特色服务:', service)

    wx.showToast({
      title: `${service.title}功能开发中`,
      icon: 'none'
    })
  },

  /**
   * 查看更多点击事件
   */
  onViewMore() {
    wx.showToast({
      title: '更多功能开发中',
      icon: 'none'
    })
  },

  /**
   * 加载轮播图数据
   */
  loadBannerData() {
    // 这里可以从后端API获取轮播图数据
    // 目前使用静态数据
    console.log('轮播图数据加载完成')
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新')
    // 重新加载数据
    this.loadBannerData()
    
    // 停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    console.log('上拉触底')
    // TODO: 加载更多数据
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'AI证件照制作小程序',
      path: '/pages/index/index',
      imageUrl: '/images/share.jpg'
    }
  }
})

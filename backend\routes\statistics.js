const express = require('express');
const router = express.Router();
const ProcessRecord = require('../models/ProcessRecord');
const UserWork = require('../models/UserWork');

// 获取探索页面统计数据
router.get('/explore-counts', async (req, res) => {
  try {
    // 获取各类型处理的统计数据
    const [
      idPhotoCount,
      enhancementCount,
      colorizationCount,
      upscalingCount,
      translationCount,
      totalProcessed
    ] = await Promise.all([
      ProcessRecord.countDocuments({ processType: 'idPhotoCreation', status: 'completed' }),
      ProcessRecord.countDocuments({ processType: 'portraitEnhancement', status: 'completed' }),
      ProcessRecord.countDocuments({ processType: 'imageColorization', status: 'completed' }),
      ProcessRecord.countDocuments({ processType: 'imageUpscaling', status: 'completed' }),
      ProcessRecord.countDocuments({ processType: 'imageTranslation', status: 'completed' }),
      ProcessRecord.countDocuments({ status: 'completed' })
    ]);

    res.json({
      success: true,
      data: {
        zjzCount: idPhotoCount,
        generateLayoutCount: enhancementCount,
        colourizeCount: colorizationCount,
        mattingCount: upscalingCount,
        cartoonCount: translationCount,
        editImageCount: totalProcessed
      },
      message: '获取探索统计数据成功'
    });

  } catch (error) {
    console.error('获取探索统计数据失败:', error);
    res.status(500).json({
      success: false,
      error: '获取探索统计数据失败',
      message: error.message
    });
  }
});

// 获取系统总体统计
router.get('/overview', async (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    // 构建时间查询条件
    const timeQuery = {};
    if (startDate || endDate) {
      timeQuery.createdAt = {};
      if (startDate) timeQuery.createdAt.$gte = new Date(startDate);
      if (endDate) timeQuery.createdAt.$lte = new Date(endDate);
    }

    // 获取处理记录统计
    const [
      totalProcessed,
      successfulProcessed,
      failedProcessed,
      processingTime,
      typeStats
    ] = await Promise.all([
      ProcessRecord.countDocuments(timeQuery),
      ProcessRecord.countDocuments({ ...timeQuery, status: 'completed' }),
      ProcessRecord.countDocuments({ ...timeQuery, status: 'failed' }),
      ProcessRecord.aggregate([
        { $match: { ...timeQuery, status: 'completed', 'timing.duration': { $exists: true } } },
        {
          $group: {
            _id: null,
            avgDuration: { $avg: '$timing.duration' },
            minDuration: { $min: '$timing.duration' },
            maxDuration: { $max: '$timing.duration' }
          }
        }
      ]),
      ProcessRecord.aggregate([
        { $match: timeQuery },
        {
          $group: {
            _id: '$processType',
            count: { $sum: 1 },
            successCount: {
              $sum: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
            },
            failCount: {
              $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] }
            }
          }
        }
      ])
    ]);

    // 获取用户作品统计
    const [
      totalWorks,
      favoriteWorks,
      publicWorks,
      totalDownloads,
      totalShares
    ] = await Promise.all([
      UserWork.countDocuments(timeQuery),
      UserWork.countDocuments({ ...timeQuery, isFavorite: true }),
      UserWork.countDocuments({ ...timeQuery, isPublic: true }),
      UserWork.aggregate([
        { $match: timeQuery },
        { $group: { _id: null, total: { $sum: '$downloadCount' } } }
      ]),
      UserWork.aggregate([
        { $match: timeQuery },
        { $group: { _id: null, total: { $sum: '$shareCount' } } }
      ])
    ]);

    res.json({
      success: true,
      data: {
        processing: {
          total: totalProcessed,
          successful: successfulProcessed,
          failed: failedProcessed,
          successRate: totalProcessed > 0 ? (successfulProcessed / totalProcessed * 100).toFixed(2) : 0,
          avgDuration: processingTime[0]?.avgDuration || 0,
          minDuration: processingTime[0]?.minDuration || 0,
          maxDuration: processingTime[0]?.maxDuration || 0,
          byType: typeStats
        },
        works: {
          total: totalWorks,
          favorite: favoriteWorks,
          public: publicWorks,
          totalDownloads: totalDownloads[0]?.total || 0,
          totalShares: totalShares[0]?.total || 0
        }
      },
      message: '获取系统统计数据成功'
    });

  } catch (error) {
    console.error('获取系统统计数据失败:', error);
    res.status(500).json({
      success: false,
      error: '获取系统统计数据失败',
      message: error.message
    });
  }
});

// 获取用户活跃度统计
router.get('/user-activity', async (req, res) => {
  try {
    const { days = 7 } = req.query;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days));

    // 按天统计用户活跃度
    const dailyActivity = await ProcessRecord.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          status: 'completed'
        }
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
            userId: '$userId'
          }
        }
      },
      {
        $group: {
          _id: '$_id.date',
          activeUsers: { $sum: 1 },
          date: { $first: '$_id.date' }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    // 按小时统计处理量
    const hourlyProcessing = await ProcessRecord.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate },
          status: 'completed'
        }
      },
      {
        $group: {
          _id: { $hour: '$createdAt' },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.json({
      success: true,
      data: {
        dailyActivity,
        hourlyProcessing,
        period: `${days}天`
      },
      message: '获取用户活跃度统计成功'
    });

  } catch (error) {
    console.error('获取用户活跃度统计失败:', error);
    res.status(500).json({
      success: false,
      error: '获取用户活跃度统计失败',
      message: error.message
    });
  }
});

// 获取热门功能统计
router.get('/popular-features', async (req, res) => {
  try {
    const { limit = 10 } = req.query;

    // 获取最受欢迎的处理类型
    const popularTypes = await ProcessRecord.aggregate([
      { $match: { status: 'completed' } },
      {
        $group: {
          _id: '$processType',
          count: { $sum: 1 },
          avgDuration: { $avg: '$timing.duration' },
          successRate: {
            $avg: { $cond: [{ $eq: ['$status', 'completed'] }, 1, 0] }
          }
        }
      },
      { $sort: { count: -1 } },
      { $limit: parseInt(limit) }
    ]);

    // 获取最受欢迎的证件照尺寸
    const popularSizes = await ProcessRecord.aggregate([
      { 
        $match: { 
          processType: 'idPhotoCreation',
          status: 'completed',
          'parameters.photoSize': { $exists: true }
        }
      },
      {
        $group: {
          _id: '$parameters.photoSize.name',
          count: { $sum: 1 },
          sizeInfo: { $first: '$parameters.photoSize' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: parseInt(limit) }
    ]);

    res.json({
      success: true,
      data: {
        popularTypes,
        popularSizes
      },
      message: '获取热门功能统计成功'
    });

  } catch (error) {
    console.error('获取热门功能统计失败:', error);
    res.status(500).json({
      success: false,
      error: '获取热门功能统计失败',
      message: error.message
    });
  }
});

module.exports = router;

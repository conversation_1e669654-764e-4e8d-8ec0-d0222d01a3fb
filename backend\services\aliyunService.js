const OSS = require('ali-oss');
const Core = require('@alicloud/pop-core');
const fs = require('fs');
const path = require('path');

// 测试阿里云连接
async function testAliyunConnection(config) {
  try {
    if (!config.accessKeyId || !config.accessKeySecret) {
      return {
        success: false,
        message: '请配置阿里云AccessKey'
      };
    }

    // 测试OSS连接
    if (config.oss && config.oss.bucket) {
      const client = new OSS({
        region: config.oss.region || 'oss-cn-hangzhou',
        accessKeyId: config.accessKeyId,
        accessKeySecret: config.accessKeySecret,
        bucket: config.oss.bucket
      });

      await client.getBucketInfo();
    }

    return {
      success: true,
      message: '阿里云连接测试成功'
    };
  } catch (error) {
    return {
      success: false,
      message: `阿里云连接测试失败: ${error.message}`
    };
  }
}

// 上传文件到阿里云OSS
async function uploadToAliyun(filePath, filename, config) {
  try {
    if (!config.oss || !config.oss.bucket) {
      throw new Error('OSS配置不完整');
    }

    const client = new OSS({
      region: config.oss.region || 'oss-cn-hangzhou',
      accessKeyId: config.accessKeyId,
      accessKeySecret: config.accessKeySecret,
      bucket: config.oss.bucket
    });

    const result = await client.put(filename, filePath);
    
    return {
      success: true,
      url: result.url,
      name: result.name
    };
  } catch (error) {
    console.error('阿里云上传失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 从阿里云OSS删除文件
async function deleteFromAliyun(filename, config) {
  try {
    if (!config.oss || !config.oss.bucket) {
      throw new Error('OSS配置不完整');
    }

    const client = new OSS({
      region: config.oss.region || 'oss-cn-hangzhou',
      accessKeyId: config.accessKeyId,
      accessKeySecret: config.accessKeySecret,
      bucket: config.oss.bucket
    });

    await client.delete(filename);
    
    return {
      success: true,
      message: '文件删除成功'
    };
  } catch (error) {
    console.error('阿里云删除失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 创建阿里云视觉AI客户端
function createVisionClient(config, service = 'imagerecog') {
  const endpoints = {
    imagerecog: 'https://imagerecog.cn-shanghai.aliyuncs.com',
    imageseg: 'https://imageseg.cn-shanghai.aliyuncs.com'
  };

  return new Core({
    accessKeyId: config.accessKeyId,
    accessKeySecret: config.accessKeySecret,
    endpoint: endpoints[service],
    apiVersion: '2019-12-30'
  });
}

// 证件照制作 - 人像分割
async function createIdPhoto(imageUrl, config) {
  try {
    const client = createVisionClient(config, 'imageseg');

    const params = {
      ImageURL: imageUrl
    };

    const requestOption = {
      method: 'POST'
    };

    const result = await client.request('SegmentHDBody', params, requestOption);

    if (result.Data && result.Data.ImageURL) {
      return {
        success: true,
        data: {
          segmentedImageUrl: result.Data.ImageURL,
          originalImageUrl: imageUrl
        }
      };
    } else {
      return {
        success: false,
        error: '人像分割失败'
      };
    }
  } catch (error) {
    console.error('证件照制作失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 人像增强
async function enhancePortrait(imageUrl, config) {
  try {
    const client = createVisionClient(config);
    
    const params = {
      ImageURL: imageUrl
    };

    const requestOption = {
      method: 'POST'
    };

    const result = await client.request('EnhancePortrait', params, requestOption);
    
    if (result.Data && result.Data.ImageURL) {
      return {
        success: true,
        data: {
          enhancedImageUrl: result.Data.ImageURL
        }
      };
    } else {
      return {
        success: false,
        error: '人像增强失败'
      };
    }
  } catch (error) {
    console.error('人像增强失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 图片上色
async function colorizeImage(imageUrl, config) {
  try {
    const client = createVisionClient(config);
    
    const params = {
      ImageURL: imageUrl
    };

    const requestOption = {
      method: 'POST'
    };

    const result = await client.request('ImageColorization', params, requestOption);
    
    if (result.Data && result.Data.ImageURL) {
      return {
        success: true,
        data: {
          colorizedImageUrl: result.Data.ImageURL
        }
      };
    } else {
      return {
        success: false,
        error: '图片上色失败'
      };
    }
  } catch (error) {
    console.error('图片上色失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 图片放大
async function upscaleImage(imageUrl, scale, config) {
  try {
    const client = createVisionClient(config);
    
    const params = {
      ImageURL: imageUrl,
      Scale: scale
    };

    const requestOption = {
      method: 'POST'
    };

    const result = await client.request('ImageUpscaling', params, requestOption);
    
    if (result.Data && result.Data.ImageURL) {
      return {
        success: true,
        data: {
          upscaledImageUrl: result.Data.ImageURL
        }
      };
    } else {
      return {
        success: false,
        error: '图片放大失败'
      };
    }
  } catch (error) {
    console.error('图片放大失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 图片翻译
async function translateImage(imageUrl, targetLanguage, config) {
  try {
    const client = createVisionClient(config);
    
    const params = {
      ImageURL: imageUrl,
      TargetLanguage: targetLanguage
    };

    const requestOption = {
      method: 'POST'
    };

    const result = await client.request('TranslateImage', params, requestOption);
    
    if (result.Data && result.Data.OcrResult) {
      return {
        success: true,
        data: {
          originalText: result.Data.OcrResult,
          translatedText: result.Data.TranslatedText || '翻译结果'
        }
      };
    } else {
      return {
        success: false,
        error: '图片翻译失败'
      };
    }
  } catch (error) {
    console.error('图片翻译失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 背景替换功能
async function replaceBackground(imageUrl, backgroundColor, config) {
  try {
    const client = createVisionClient(config, 'imageseg');

    // 首先进行人像分割
    const segmentParams = {
      ImageURL: imageUrl
    };

    const requestOption = {
      method: 'POST'
    };

    const segmentResult = await client.request('SegmentHDBody', segmentParams, requestOption);

    if (!segmentResult.Data || !segmentResult.Data.ImageURL) {
      return {
        success: false,
        error: '人像分割失败'
      };
    }

    // TODO: 这里需要实现背景替换逻辑
    // 由于阿里云视觉AI没有直接的背景替换API，我们需要：
    // 1. 使用分割后的图片
    // 2. 通过图像处理库（如sharp）添加指定颜色的背景
    // 3. 或者调用其他支持背景替换的AI服务

    // 暂时返回分割后的图片作为处理结果
    return {
      success: true,
      data: {
        processedImageUrl: segmentResult.Data.ImageURL,
        originalImageUrl: imageUrl,
        backgroundColor: backgroundColor,
        segmentedImageUrl: segmentResult.Data.ImageURL
      }
    };

  } catch (error) {
    console.error('背景替换失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  testAliyunConnection,
  uploadToAliyun,
  deleteFromAliyun,
  createIdPhoto,
  enhancePortrait,
  colorizeImage,
  upscaleImage,
  translateImage,
  replaceBackground
};

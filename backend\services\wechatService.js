const axios = require('axios');

/**
 * 微信小程序服务
 */
class WechatService {
  constructor() {
    this.appId = process.env.WECHAT_APPID;
    this.appSecret = process.env.WECHAT_SECRET;
    this.baseURL = 'https://api.weixin.qq.com';
  }

  /**
   * 验证微信小程序配置
   */
  validateConfig() {
    if (!this.appId || !this.appSecret) {
      throw new Error('微信小程序配置不完整，请检查 WECHAT_APPID 和 WECHAT_SECRET 环境变量');
    }
  }

  /**
   * 通过code获取用户的openid和session_key
   * @param {string} code - 微信小程序登录时获取的code
   * @returns {Promise<Object>} 包含openid、session_key等信息
   */
  async code2Session(code) {
    try {
      this.validateConfig();

      const url = `${this.baseURL}/sns/jscode2session`;
      const params = {
        appid: this.appId,
        secret: this.appSecret,
        js_code: code,
        grant_type: 'authorization_code'
      };

      console.log('调用微信API:', url, { appid: this.appId, js_code: code });

      const response = await axios.get(url, { params });
      const data = response.data;

      console.log('微信API响应:', data);

      // 检查是否有错误
      if (data.errcode) {
        throw new Error(`微信API错误: ${data.errcode} - ${data.errmsg}`);
      }

      // 验证必要字段
      if (!data.openid) {
        throw new Error('微信API返回数据异常：缺少openid');
      }

      return {
        success: true,
        data: {
          openid: data.openid,
          session_key: data.session_key,
          unionid: data.unionid || null
        }
      };

    } catch (error) {
      console.error('微信登录验证失败:', error);
      
      // 如果是网络错误或微信服务器错误，返回更友好的错误信息
      if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        return {
          success: false,
          error: '网络连接失败，请检查网络连接',
          code: 'NETWORK_ERROR'
        };
      }

      // 如果是微信API错误
      if (error.message.includes('微信API错误')) {
        return {
          success: false,
          error: error.message,
          code: 'WECHAT_API_ERROR'
        };
      }

      // 配置错误
      if (error.message.includes('配置不完整')) {
        return {
          success: false,
          error: error.message,
          code: 'CONFIG_ERROR'
        };
      }

      return {
        success: false,
        error: error.message || '微信登录验证失败',
        code: 'UNKNOWN_ERROR'
      };
    }
  }

  /**
   * 获取微信小程序access_token
   * @returns {Promise<Object>} access_token信息
   */
  async getAccessToken() {
    try {
      this.validateConfig();

      const url = `${this.baseURL}/cgi-bin/token`;
      const params = {
        grant_type: 'client_credential',
        appid: this.appId,
        secret: this.appSecret
      };

      const response = await axios.get(url, { params });
      const data = response.data;

      if (data.errcode) {
        throw new Error(`获取access_token失败: ${data.errcode} - ${data.errmsg}`);
      }

      return {
        success: true,
        data: {
          access_token: data.access_token,
          expires_in: data.expires_in
        }
      };

    } catch (error) {
      console.error('获取access_token失败:', error);
      return {
        success: false,
        error: error.message || '获取access_token失败'
      };
    }
  }

  /**
   * 解密微信小程序数据
   * @param {string} encryptedData - 加密数据
   * @param {string} iv - 初始向量
   * @param {string} sessionKey - 会话密钥
   * @returns {Object} 解密后的数据
   */
  decryptData(encryptedData, iv, sessionKey) {
    try {
      const crypto = require('crypto');
      
      // Base64解码
      const sessionKeyBuffer = Buffer.from(sessionKey, 'base64');
      const encryptedDataBuffer = Buffer.from(encryptedData, 'base64');
      const ivBuffer = Buffer.from(iv, 'base64');

      // AES解密
      const decipher = crypto.createDecipheriv('aes-128-cbc', sessionKeyBuffer, ivBuffer);
      decipher.setAutoPadding(true);
      
      let decrypted = decipher.update(encryptedDataBuffer, null, 'utf8');
      decrypted += decipher.final('utf8');

      const decryptedData = JSON.parse(decrypted);

      // 验证水印
      if (decryptedData.watermark.appid !== this.appId) {
        throw new Error('数据水印验证失败');
      }

      return {
        success: true,
        data: decryptedData
      };

    } catch (error) {
      console.error('数据解密失败:', error);
      return {
        success: false,
        error: error.message || '数据解密失败'
      };
    }
  }

  /**
   * 验证微信小程序签名
   * @param {string} rawData - 原始数据
   * @param {string} signature - 签名
   * @param {string} sessionKey - 会话密钥
   * @returns {boolean} 验证结果
   */
  verifySignature(rawData, signature, sessionKey) {
    try {
      const crypto = require('crypto');
      const hash = crypto.createHash('sha1');
      hash.update(rawData + sessionKey);
      const calculatedSignature = hash.digest('hex');
      
      return calculatedSignature === signature;
    } catch (error) {
      console.error('签名验证失败:', error);
      return false;
    }
  }

  /**
   * 检查微信服务配置状态
   * @returns {Object} 配置状态
   */
  getConfigStatus() {
    return {
      configured: !!(this.appId && this.appSecret),
      appId: this.appId ? this.appId.substring(0, 8) + '...' : null,
      hasSecret: !!this.appSecret
    };
  }
}

// 创建单例实例
const wechatService = new WechatService();

module.exports = wechatService;

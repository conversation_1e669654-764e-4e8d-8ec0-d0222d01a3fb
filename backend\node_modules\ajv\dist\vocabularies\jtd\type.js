"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.intRange = void 0;
const codegen_1 = require("../../compile/codegen");
const timestamp_1 = require("../../compile/timestamp");
const util_1 = require("../../compile/util");
const metadata_1 = require("./metadata");
exports.intRange = {
    int8: [-128, 127, 3],
    uint8: [0, 255, 3],
    int16: [-32768, 32767, 5],
    uint16: [0, 65535, 5],
    int32: [-2147483648, 2147483647, 10],
    uint32: [0, 4294967295, 10],
};
const def = {
    keyword: "type",
    schemaType: "string",
    code(cxt) {
        metadata_1.checkMetadata(cxt);
        const { gen, data, schema, parentSchema } = cxt;
        let cond;
        switch (schema) {
            case "boolean":
            case "string":
                cond = codegen_1._ `typeof ${data} == ${schema}`;
                break;
            case "timestamp": {
                const vts = util_1.func(gen, timestamp_1.default);
                cond = codegen_1._ `${data} instanceof Date || (typeof ${data} == "string" && ${vts}(${data}))`;
                break;
            }
            case "float32":
            case "float64":
                cond = codegen_1._ `typeof ${data} == "number"`;
                break;
            default: {
                const [min, max] = exports.intRange[schema];
                cond = codegen_1._ `typeof ${data} == "number" && isFinite(${data}) && ${data} >= ${min} && ${data} <= ${max} && !(${data} % 1)`;
            }
        }
        cxt.pass(parentSchema.nullable ? codegen_1.or(codegen_1._ `${data} === null`, cond) : cond);
    },
};
exports.default = def;
//# sourceMappingURL=type.js.map
const {
  createIdPhoto: aliyunCreateIdPhoto,
  enhancePortrait: aliyunEnhancePortrait,
  colorizeImage: aliyunColorizeImage,
  upscaleImage: aliyunUpscaleImage,
  translateImage: aliyunTranslateImage
} = require('./aliyunService');

// 证件照制作
async function createIdPhoto(imageUrl, config) {
  try {
    // 使用阿里云的人像分割服务制作证件照
    return await aliyunCreateIdPhoto(imageUrl, config);
  } catch (error) {
    console.error('证件照制作服务失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 人像增强
async function enhancePortrait(imageUrl, config) {
  try {
    // 目前主要使用阿里云的人像增强服务
    return await aliyunEnhancePortrait(imageUrl, config);
  } catch (error) {
    console.error('人像增强服务失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 图片上色
async function colorizeImage(imageUrl, config) {
  try {
    // 目前主要使用阿里云的图片上色服务
    return await aliyunColorizeImage(imageUrl, config);
  } catch (error) {
    console.error('图片上色服务失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 图片放大
async function upscaleImage(imageUrl, scale, config) {
  try {
    // 目前主要使用阿里云的图片放大服务
    return await aliyunUpscaleImage(imageUrl, scale, config);
  } catch (error) {
    console.error('图片放大服务失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 图片翻译
async function translateImage(imageUrl, targetLanguage, config) {
  try {
    // 目前主要使用阿里云的图片翻译服务
    return await aliyunTranslateImage(imageUrl, targetLanguage, config);
  } catch (error) {
    console.error('图片翻译服务失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 获取所有可用的AI服务
function getAvailableServices() {
  return [
    {
      id: 'idPhotoCreation',
      name: '证件照制作',
      description: '智能人像分割，制作专业证件照',
      category: 'photo',
      featured: true
    },
    {
      id: 'portraitEnhancement',
      name: 'AI人像变清晰',
      description: '提升人像照片的清晰度和质量',
      category: 'enhancement',
      featured: true
    },
    {
      id: 'imageColorization',
      name: '黑白照片上色',
      description: '为黑白照片智能添加颜色',
      category: 'enhancement',
      featured: false
    },
    {
      id: 'imageUpscaling',
      name: '图片无损放大',
      description: '提升图片分辨率，保持清晰度',
      category: 'enhancement',
      featured: false
    },
    {
      id: 'imageTranslation',
      name: '图片翻译',
      description: '识别并翻译图片中的文字',
      category: 'text',
      featured: false
    },
    {
      id: 'portraitClarification',
      name: '人像变清晰',
      description: '优化人像照片质量',
      category: 'enhancement',
      featured: false
    }
  ];
}

// 获取主推服务（首页展示）
function getFeaturedServices() {
  return getAvailableServices().filter(service => service.featured);
}

module.exports = {
  createIdPhoto,
  enhancePortrait,
  colorizeImage,
  upscaleImage,
  translateImage,
  getAvailableServices,
  getFeaturedServices
};

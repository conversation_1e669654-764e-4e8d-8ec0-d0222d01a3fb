{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../lib/compile/errors.ts"], "names": [], "mappings": ";;;AAEA,uCAAgE;AAEhE,mCAAuB;AAEV,QAAA,YAAY,GAA2B;IAClD,OAAO,EAAE,CAAC,EAAC,OAAO,EAAC,EAAE,EAAE,CAAC,aAAG,CAAA,gBAAgB,OAAO,sBAAsB;CACzE,CAAA;AAEY,QAAA,iBAAiB,GAA2B;IACvD,OAAO,EAAE,CAAC,EAAC,OAAO,EAAE,UAAU,EAAC,EAAE,EAAE,CACjC,UAAU;QACR,CAAC,CAAC,aAAG,CAAA,IAAI,OAAO,qBAAqB,UAAU,UAAU;QACzD,CAAC,CAAC,aAAG,CAAA,IAAI,OAAO,8BAA8B;CACnD,CAAA;AAED,SAAgB,WAAW,CACzB,GAAoB,EACpB,QAAgC,oBAAY,EAC5C,iBAA2B;IAE3B,MAAM,EAAC,EAAE,EAAC,GAAG,GAAG,CAAA;IAChB,MAAM,EAAC,GAAG,EAAE,aAAa,EAAE,SAAS,EAAC,GAAG,EAAE,CAAA;IAC1C,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IAC1C,IAAI,iBAAiB,aAAjB,iBAAiB,cAAjB,iBAAiB,GAAI,CAAC,aAAa,IAAI,SAAS,CAAC,EAAE;QACrD,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;KACtB;SAAM;QACL,YAAY,CAAC,EAAE,EAAE,WAAC,CAAA,IAAI,MAAM,GAAG,CAAC,CAAA;KACjC;AACH,CAAC;AAbD,kCAaC;AAED,SAAgB,gBAAgB,CAC9B,GAAoB,EACpB,QAAgC,oBAAY;IAE5C,MAAM,EAAC,EAAE,EAAC,GAAG,GAAG,CAAA;IAChB,MAAM,EAAC,GAAG,EAAE,aAAa,EAAE,SAAS,EAAC,GAAG,EAAE,CAAA;IAC1C,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IAC1C,QAAQ,CAAC,GAAG,EAAE,MAAM,CAAC,CAAA;IACrB,IAAI,CAAC,CAAC,aAAa,IAAI,SAAS,CAAC,EAAE;QACjC,YAAY,CAAC,EAAE,EAAE,eAAC,CAAC,OAAO,CAAC,CAAA;KAC5B;AACH,CAAC;AAXD,4CAWC;AAED,SAAgB,gBAAgB,CAAC,GAAY,EAAE,SAAe;IAC5D,GAAG,CAAC,MAAM,CAAC,eAAC,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;IAC/B,GAAG,CAAC,EAAE,CAAC,WAAC,CAAA,GAAG,eAAC,CAAC,OAAO,WAAW,EAAE,GAAG,EAAE,CACpC,GAAG,CAAC,EAAE,CACJ,SAAS,EACT,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,WAAC,CAAA,GAAG,eAAC,CAAC,OAAO,SAAS,EAAE,SAAS,CAAC,EACnD,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,eAAC,CAAC,OAAO,EAAE,IAAI,CAAC,CAClC,CACF,CAAA;AACH,CAAC;AATD,4CASC;AAED,SAAgB,YAAY,CAAC,EAC3B,GAAG,EACH,OAAO,EACP,WAAW,EACX,IAAI,EACJ,SAAS,EACT,EAAE,GACc;IAChB,wBAAwB;IACxB,IAAI,SAAS,KAAK,SAAS;QAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;IACxE,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IAC3B,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,SAAS,EAAE,eAAC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAE,EAAE;QAC3C,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,WAAC,CAAA,GAAG,eAAC,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;QACrC,GAAG,CAAC,EAAE,CAAC,WAAC,CAAA,GAAG,GAAG,yBAAyB,EAAE,GAAG,EAAE,CAC5C,GAAG,CAAC,MAAM,CAAC,WAAC,CAAA,GAAG,GAAG,WAAW,EAAE,mBAAS,CAAC,eAAC,CAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,CAAC,CACpE,CAAA;QACD,GAAG,CAAC,MAAM,CAAC,WAAC,CAAA,GAAG,GAAG,aAAa,EAAE,aAAG,CAAA,GAAG,EAAE,CAAC,aAAa,IAAI,OAAO,EAAE,CAAC,CAAA;QACrE,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE;YACnB,GAAG,CAAC,MAAM,CAAC,WAAC,CAAA,GAAG,GAAG,SAAS,EAAE,WAAW,CAAC,CAAA;YACzC,GAAG,CAAC,MAAM,CAAC,WAAC,CAAA,GAAG,GAAG,OAAO,EAAE,IAAI,CAAC,CAAA;SACjC;IACH,CAAC,CAAC,CAAA;AACJ,CAAC;AAtBD,oCAsBC;AAED,SAAS,QAAQ,CAAC,GAAY,EAAE,MAAY;IAC1C,MAAM,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,CAAA;IACpC,GAAG,CAAC,EAAE,CACJ,WAAC,CAAA,GAAG,eAAC,CAAC,OAAO,WAAW,EACxB,GAAG,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,eAAC,CAAC,OAAO,EAAE,WAAC,CAAA,IAAI,GAAG,GAAG,CAAC,EACxC,WAAC,CAAA,GAAG,eAAC,CAAC,OAAO,SAAS,GAAG,GAAG,CAC7B,CAAA;IACD,GAAG,CAAC,IAAI,CAAC,WAAC,CAAA,GAAG,eAAC,CAAC,MAAM,IAAI,CAAC,CAAA;AAC5B,CAAC;AAED,SAAS,YAAY,CAAC,EAAa,EAAE,IAAU;IAC7C,MAAM,EAAC,GAAG,EAAE,YAAY,EAAE,SAAS,EAAC,GAAG,EAAE,CAAA;IACzC,IAAI,SAAS,CAAC,MAAM,EAAE;QACpB,GAAG,CAAC,KAAK,CAAC,WAAC,CAAA,OAAO,EAAE,CAAC,eAAuB,IAAI,IAAI,GAAG,CAAC,CAAA;KACzD;SAAM;QACL,GAAG,CAAC,MAAM,CAAC,WAAC,CAAA,GAAG,YAAY,SAAS,EAAE,IAAI,CAAC,CAAA;QAC3C,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;KAClB;AACH,CAAC;AAED,MAAM,CAAC,GAAG;IACR,OAAO,EAAE,IAAI,cAAI,CAAC,SAAS,CAAC;IAC5B,UAAU,EAAE,IAAI,cAAI,CAAC,YAAY,CAAC;IAClC,MAAM,EAAE,IAAI,cAAI,CAAC,QAAQ,CAAC;IAC1B,YAAY,EAAE,IAAI,cAAI,CAAC,cAAc,CAAC;IACtC,OAAO,EAAE,IAAI,cAAI,CAAC,SAAS,CAAC;IAC5B,MAAM,EAAE,IAAI,cAAI,CAAC,QAAQ,CAAC;IAC1B,YAAY,EAAE,IAAI,cAAI,CAAC,cAAc,CAAC;IACtC,uBAAuB;IACvB,YAAY,EAAE,IAAI,cAAI,CAAC,cAAc,CAAC;CACvC,CAAA;AAED,SAAS,eAAe,CAAC,GAAoB,EAAE,KAA6B;IAC1E,MAAM,EAAC,YAAY,EAAE,IAAI,EAAC,GAAG,GAAG,CAAC,EAAE,CAAA;IACnC,IAAI,YAAY,KAAK,KAAK;QAAE,OAAO,WAAC,CAAA,IAAI,CAAA;IACxC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;AACpF,CAAC;AAED,SAAS,cAAc,CAAC,GAAoB,EAAE,EAAC,OAAO,EAAyB;IAC7E,MAAM,EAAC,GAAG,EAAE,OAAO,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;IAC9B,MAAM,EAAC,SAAS,EAAE,aAAa,EAAE,IAAI,EAAC,GAAG,EAAE,CAAA;IAC3C,MAAM,SAAS,GAAgC;QAC7C,CAAC,CAAC,CAAC,YAAY,EAAE,mBAAS,CAAC,eAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC,UAAU,EAAE,aAAG,CAAA,GAAG,aAAa,IAAI,OAAO,EAAE,CAAC;KACjD,CAAA;IACD,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAA;KACnF;IACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,CAAA;AACjC,CAAC;AAED,SAAS,cAAc,CAAC,GAAoB,EAAE,KAA6B;IACzE,MAAM,EAAC,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;IACjD,MAAM,EAAC,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,IAAI,EAAC,GAAG,EAAE,CAAA;IACnF,MAAM,EAAC,MAAM,EAAE,OAAO,EAAC,GAAG,KAAK,CAAA;IAC/B,MAAM,SAAS,GAAgC;QAC7C,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC;QACpB,CAAC,eAAC,CAAC,QAAQ,EAAE,mBAAS,CAAC,eAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC,UAAU,EAAE,aAAG,CAAA,GAAG,aAAa,IAAI,OAAO,EAAE,CAAC;QAChD,CAAC,CAAC,CAAC,MAAM,EAAE,OAAO,MAAM,IAAI,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,WAAC,CAAA,IAAI,CAAC;KACxE,CAAA;IACD,IAAI,YAAY;QAAE,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC,CAAA;IAChE,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,OAAO,IAAI,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAA;KACnF;IACD,IAAI,IAAI,CAAC,OAAO,EAAE;QAChB,SAAS,CAAC,IAAI,CACZ,CAAC,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,EACvB,CAAC,CAAC,CAAC,YAAY,EAAE,WAAC,CAAA,GAAG,YAAY,GAAG,UAAU,EAAE,CAAC,EACjD,CAAC,eAAC,CAAC,IAAI,EAAE,IAAI,CAAC,CACf,CAAA;KACF;IACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,CAAA;AACjC,CAAC"}
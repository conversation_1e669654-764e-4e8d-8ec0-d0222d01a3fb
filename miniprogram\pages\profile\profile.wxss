/* pages/profile/profile.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 用户信息区域 */
.user-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  background-color: #f0f0f0;
}

.user-details {
  flex: 1;
}

.nickname {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.user-id {
  font-size: 24rpx;
  color: #999;
}

.login-btn {
  background-color: #07c160;
  color: white;
  border-radius: 40rpx;
  padding: 0 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  border: none;
}

.login-btn:active {
  background-color: #06ad56;
}

/* 功能菜单区域 */
.menu-section {
  background-color: white;
  margin-bottom: 20rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 30rpx;
  width: 50rpx;
  text-align: center;
}

.menu-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
}

.menu-arrow {
  font-size: 28rpx;
  color: #ccc;
}

/* 统计信息区域 */
.stats-section {
  background-color: white;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stats-item {
  text-align: center;
}

.stats-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #07c160;
  margin-bottom: 10rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #999;
}
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#root {
  width: 100%;
  height: 100vh;
}

/* 自定义样式 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.ant-layout-header {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.ant-statistic-title {
  font-size: 14px;
  color: #666;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-content {
    margin: 16px;
    padding: 16px;
  }

  .ant-statistic-content {
    font-size: 20px;
  }
}

/* 自定义组件样式 */
.config-form .ant-form-item {
  margin-bottom: 16px;
}

.status-tag {
  font-weight: 500;
}

.image-preview {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
}

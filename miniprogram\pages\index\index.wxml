<!--pages/index/index.wxml-->
<view class="page-container">
  <!-- 顶部状态栏占位 -->
  <view class="status-bar" style="height: {{statusBarHeight}}px;"></view>

  <!-- 自定义导航栏 -->
  <view class="custom-navbar">
    <view class="navbar-content">
      <view class="navbar-title">AI证件照制作</view>
    </view>
    <!-- 分界线 -->
    <view class="navbar-divider"></view>
  </view>

  <!-- 搜索区域 -->
  <view class="search-section">
    <view class="search-container">
      <view class="search-box">
        <view class="search-icon">🔍</view>
        <input
          class="search-input"
          placeholder="搜索证件照制作服务..."
          value="{{searchValue}}"
          bindinput="onSearchInput"
          confirm-type="search"
          bindconfirm="onSearch"
        />
      </view>
      <view class="search-btn" bindtap="onSearch">
        <text class="search-btn-text">搜索</text>
      </view>
    </view>
  </view>

  <!-- 轮播图区域 -->
  <view class="banner-section">
    <swiper
      class="banner-swiper"
      indicator-dots="{{true}}"
      autoplay="{{true}}"
      interval="{{4000}}"
      duration="{{800}}"
      indicator-color="rgba(255,255,255,0.4)"
      indicator-active-color="#ffffff"
      circular="{{true}}"
      easing-function="easeInOutCubic"
    >
      <swiper-item wx:for="{{bannerList}}" wx:key="id">
        <view class="banner-item" bindtap="onBannerTap" data-item="{{item}}">
          <image
            class="banner-image"
            src="{{item.imageUrl}}"
            mode="aspectFill"
          />
          <view class="banner-overlay"></view>
          <view class="banner-content">
            <view class="banner-badge">{{item.badge}}</view>
            <view class="banner-title">{{item.title}}</view>
            <view class="banner-desc">{{item.description}}</view>
            <view class="banner-action">
              <text class="action-text">立即体验</text>
              <text class="action-arrow">→</text>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 快捷功能区域 -->
  <view class="quick-actions">
    <view class="section-header">
      <view class="section-title">
        <text class="title-icon">⚡</text>
        <text class="title-text">快捷功能</text>
      </view>
      <view class="section-more" bindtap="onViewMore">
        <text>更多</text>
        <text class="more-arrow">›</text>
      </view>
    </view>

    <view class="actions-grid">
      <view class="action-item" wx:for="{{quickActions}}" wx:key="id" bindtap="onActionTap" data-action="{{item}}">
        <view class="action-icon-wrapper" style="background: {{item.bgColor}};">
          <text class="action-icon">{{item.icon}}</text>
        </view>
        <view class="action-title">{{item.title}}</view>
        <view class="action-desc">{{item.desc}}</view>
      </view>
    </view>
  </view>

  <!-- 特色服务区域 -->
  <view class="featured-services">
    <view class="section-header">
      <view class="section-title">
        <text class="title-icon">✨</text>
        <text class="title-text">特色服务</text>
      </view>
    </view>

    <view class="services-list">
      <view class="service-card" wx:for="{{featuredServices}}" wx:key="id" bindtap="onServiceTap" data-service="{{item}}">
        <view class="service-image">
          <image src="{{item.imageUrl}}" mode="aspectFill" />
          <view class="service-tag">{{item.tag}}</view>
        </view>
        <view class="service-content">
          <view class="service-title">{{item.title}}</view>
          <view class="service-desc">{{item.description}}</view>
          <view class="service-features">
            <text class="feature-item" wx:for="{{item.features}}" wx:key="*this">{{item}}</text>
          </view>
          <view class="service-action">
            <text class="action-price">{{item.price}}</text>
            <view class="action-btn">立即使用</view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部间距 -->
  <view class="bottom-spacing"></view>
</view>

<!--pages/index/index.wxml-->
<view class="container">
  <!-- 搜索区域 -->
  <view class="search-section">
    <view class="search-container">
      <input 
        class="search-input" 
        placeholder="搜索证件照制作服务..." 
        value="{{searchValue}}"
        bindinput="onSearchInput"
        confirm-type="search"
        bindconfirm="onSearch"
      />
      <button class="search-btn btn-primary" bindtap="onSearch">搜索</button>
    </view>
  </view>

  <!-- 轮播图区域 -->
  <view class="banner-section">
    <swiper 
      class="banner-swiper" 
      indicator-dots="{{true}}" 
      autoplay="{{true}}" 
      interval="{{3000}}" 
      duration="{{500}}"
      indicator-color="rgba(255,255,255,0.5)"
      indicator-active-color="#07c160"
      circular="{{true}}"
    >
      <swiper-item wx:for="{{bannerList}}" wx:key="id">
        <image 
          class="banner-image" 
          src="{{item.imageUrl}}" 
          mode="aspectFill"
          bindtap="onBannerTap"
          data-item="{{item}}"
        />
        <view class="banner-content">
          <view class="banner-title">{{item.title}}</view>
          <view class="banner-desc">{{item.description}}</view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 功能区域 - 暂时预留 -->
  <view class="features-section">
    <view class="section-title">主要功能</view>
    <view class="features-grid">
      <!-- 这里后续会添加功能卡片 -->
      <view class="feature-placeholder">
        <text>功能区域待添加...</text>
      </view>
    </view>
  </view>
</view>

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Select,
  DatePicker,
  Space,
  Progress,
  Tag
} from 'antd';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { statisticsAPI } from '../services/api';

const { RangePicker } = DatePicker;
const { Option } = Select;

const Statistics = () => {
  const [overview, setOverview] = useState({
    processing: {
      total: 0,
      successful: 0,
      failed: 0,
      successRate: 0,
      avgDuration: 0,
      byType: []
    },
    works: {
      total: 0,
      favorite: 0,
      public: 0,
      totalDownloads: 0,
      totalShares: 0
    }
  });
  const [userActivity, setUserActivity] = useState({
    dailyActivity: [],
    hourlyProcessing: []
  });
  const [popularFeatures, setPopularFeatures] = useState({
    popularTypes: [],
    popularSizes: []
  });
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState(null);
  const [activityDays, setActivityDays] = useState(7);

  // 获取概览数据
  const fetchOverview = async () => {
    try {
      const params = {};
      if (dateRange) {
        params.startDate = dateRange[0].format('YYYY-MM-DD');
        params.endDate = dateRange[1].format('YYYY-MM-DD');
      }

      const response = await statisticsAPI.getOverview(params);
      if (response.success) {
        setOverview(response.data);
      }
    } catch (error) {
      console.error('获取概览数据失败:', error);
    }
  };

  // 获取用户活跃度数据
  const fetchUserActivity = async () => {
    try {
      const response = await statisticsAPI.getUserActivity({
        days: activityDays
      });
      if (response.success) {
        setUserActivity(response.data);
      }
    } catch (error) {
      console.error('获取用户活跃度数据失败:', error);
    }
  };

  // 获取热门功能数据
  const fetchPopularFeatures = async () => {
    try {
      const response = await statisticsAPI.getPopularFeatures();
      if (response.success) {
        setPopularFeatures(response.data);
      }
    } catch (error) {
      console.error('获取热门功能数据失败:', error);
    }
  };

  useEffect(() => {
    fetchOverview();
    fetchUserActivity();
    fetchPopularFeatures();
  }, []);

  useEffect(() => {
    fetchOverview();
  }, [dateRange]);

  useEffect(() => {
    fetchUserActivity();
  }, [activityDays]);

  // 图表颜色
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  // 处理类型映射
  const typeMap = {
    idPhotoCreation: '证件照制作',
    portraitEnhancement: '人像增强',
    imageColorization: '图片上色',
    imageUpscaling: '图片放大',
    imageTranslation: '图片翻译'
  };

  // 处理类型表格列
  const typeColumns = [
    {
      title: '处理类型',
      dataIndex: '_id',
      key: 'type',
      render: (type) => typeMap[type] || type
    },
    {
      title: '总数',
      dataIndex: 'count',
      key: 'count'
    },
    {
      title: '成功数',
      dataIndex: 'successCount',
      key: 'successCount'
    },
    {
      title: '失败数',
      dataIndex: 'failCount',
      key: 'failCount'
    },
    {
      title: '成功率',
      key: 'successRate',
      render: (_, record) => {
        const rate = record.count > 0 ? (record.successCount / record.count * 100).toFixed(1) : 0;
        return (
          <Progress
            percent={rate}
            size="small"
            status={rate > 90 ? 'success' : rate > 70 ? 'normal' : 'exception'}
          />
        );
      }
    }
  ];

  // 热门功能表格列
  const popularColumns = [
    {
      title: '功能类型',
      dataIndex: '_id',
      key: 'type',
      render: (type) => typeMap[type] || type
    },
    {
      title: '使用次数',
      dataIndex: 'count',
      key: 'count'
    },
    {
      title: '平均处理时间',
      dataIndex: 'avgDuration',
      key: 'avgDuration',
      render: (duration) => `${(duration / 1000).toFixed(2)}s`
    }
  ];

  // 热门尺寸表格列
  const sizeColumns = [
    {
      title: '尺寸名称',
      dataIndex: '_id',
      key: 'name'
    },
    {
      title: '使用次数',
      dataIndex: 'count',
      key: 'count'
    },
    {
      title: '尺寸信息',
      dataIndex: 'sizeInfo',
      key: 'sizeInfo',
      render: (sizeInfo) => (
        <Space>
          <Tag>{sizeInfo?.widthMm}×{sizeInfo?.heightMm}mm</Tag>
          <Tag>{sizeInfo?.widthPx}×{sizeInfo?.heightPx}px</Tag>
        </Space>
      )
    }
  ];

  return (
    <div>
      {/* 筛选器 */}
      <Card style={{ marginBottom: 16 }}>
        <Space>
          <span>时间范围:</span>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            allowClear
          />
          <span>活跃度统计天数:</span>
          <Select
            value={activityDays}
            onChange={setActivityDays}
            style={{ width: 100 }}
          >
            <Option value={7}>7天</Option>
            <Option value={15}>15天</Option>
            <Option value={30}>30天</Option>
          </Select>
        </Space>
      </Card>

      {/* 概览统计 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总处理数"
              value={overview.processing.total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="成功处理数"
              value={overview.processing.successful}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="成功率"
              value={overview.processing.successRate}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均处理时间"
              value={(overview.processing.avgDuration / 1000).toFixed(2)}
              suffix="s"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总作品数"
              value={overview.works.total}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="收藏作品"
              value={overview.works.favorite}
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总下载量"
              value={overview.works.totalDownloads}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总分享量"
              value={overview.works.totalShares}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Card title="每日活跃用户">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={userActivity.dailyActivity}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="activeUsers" stroke="#8884d8" />
              </LineChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="每小时处理量">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={userActivity.hourlyProcessing}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="_id" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="count" fill="#82ca9d" />
              </BarChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 表格区域 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Card title="处理类型统计">
            <Table
              columns={typeColumns}
              dataSource={overview.processing.byType}
              rowKey="_id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="热门功能">
            <Table
              columns={popularColumns}
              dataSource={popularFeatures.popularTypes}
              rowKey="_id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Card title="热门证件照尺寸">
            <Table
              columns={sizeColumns}
              dataSource={popularFeatures.popularSizes}
              rowKey="_id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Statistics;

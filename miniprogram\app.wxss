/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

/* 通用按钮样式 */
.btn-primary {
  background-color: #07c160;
  color: white;
  border-radius: 8rpx;
  border: none;
  font-size: 32rpx;
  font-weight: 500;
}

.btn-primary:active {
  background-color: #06ad56;
}

/* 卡片样式 */
.card {
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin: 20rpx;
  overflow: hidden;
}

/* 搜索框样式 */
.search-container {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: white;
}

.search-input {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  border: none;
}

.search-btn {
  margin-left: 20rpx;
  height: 80rpx;
  padding: 0 30rpx;
  line-height: 80rpx;
  font-size: 28rpx;
}

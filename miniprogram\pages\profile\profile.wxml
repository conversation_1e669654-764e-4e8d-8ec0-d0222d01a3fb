<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-info">
      <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill" />
      <view class="user-details">
        <view class="nickname">{{userInfo.nickName || '未登录'}}</view>
        <view class="user-id" wx:if="{{userInfo.openid}}">ID: {{userInfo.openid.slice(0, 8)}}...</view>
      </view>
      <button class="login-btn" wx:if="{{!userInfo.nickName}}" bindtap="onLogin">登录</button>
    </view>
  </view>

  <!-- 功能菜单区域 -->
  <view class="menu-section">
    <view class="menu-item" bindtap="onMenuTap" data-type="works">
      <view class="menu-icon">📷</view>
      <view class="menu-text">我的作品</view>
      <view class="menu-arrow">></view>
    </view>

    <view class="menu-item" bindtap="onMenuTap" data-type="history">
      <view class="menu-icon">📋</view>
      <view class="menu-text">处理记录</view>
      <view class="menu-arrow">></view>
    </view>

    <view class="menu-item" bindtap="onMenuTap" data-type="settings">
      <view class="menu-icon">⚙️</view>
      <view class="menu-text">设置</view>
      <view class="menu-arrow">></view>
    </view>

    <view class="menu-item" bindtap="onMenuTap" data-type="about">
      <view class="menu-icon">ℹ️</view>
      <view class="menu-text">关于我们</view>
      <view class="menu-arrow">></view>
    </view>
  </view>

  <!-- 统计信息区域 -->
  <view class="stats-section" wx:if="{{userInfo.nickName}}">
    <view class="stats-title">使用统计</view>
    <view class="stats-grid">
      <view class="stats-item">
        <view class="stats-number">{{stats.totalWorks || 0}}</view>
        <view class="stats-label">制作数量</view>
      </view>
      <view class="stats-item">
        <view class="stats-number">{{stats.totalDays || 0}}</view>
        <view class="stats-label">使用天数</view>
      </view>
    </view>
  </view>
</view>
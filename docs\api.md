# API 接口文档

## 基础信息

- 基础URL: `http://localhost:3000/api`
- 数据格式: JSON
- 字符编码: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误信息",
  "message": "详细错误描述"
}
```

## 配置管理 API

### 获取配置
- **URL**: `/config`
- **方法**: `GET`
- **描述**: 获取系统配置信息

**响应示例**:
```json
{
  "success": true,
  "data": {
    "aliyun": {
      "accessKeyId": "***",
      "region": "cn-hangzhou",
      "oss": {
        "bucket": "my-bucket",
        "region": "oss-cn-hangzhou"
      }
    },
    "system": {
      "maxFileSize": 10485760,
      "allowedFileTypes": ["image/jpeg", "image/png"]
    }
  }
}
```

### 更新阿里云配置
- **URL**: `/config/aliyun`
- **方法**: `PUT`
- **描述**: 更新阿里云配置

**请求参数**:
```json
{
  "accessKeyId": "your_access_key_id",
  "accessKeySecret": "your_access_key_secret",
  "region": "cn-hangzhou",
  "oss": {
    "bucket": "your_bucket",
    "region": "oss-cn-hangzhou",
    "endpoint": "https://oss-cn-hangzhou.aliyuncs.com"
  }
}
```

### 测试连接
- **URL**: `/config/test`
- **方法**: `POST`
- **描述**: 测试云服务连接

**请求参数**:
```json
{
  "provider": "aliyun" // 或 "tencent"
}
```

## 图片管理 API

### 上传图片
- **URL**: `/image/upload`
- **方法**: `POST`
- **描述**: 上传图片文件
- **Content-Type**: `multipart/form-data`

**请求参数**:
- `image`: 图片文件 (File)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "url": "https://example.com/image.jpg",
    "filename": "image-123456.jpg",
    "provider": "aliyun",
    "originalName": "photo.jpg",
    "size": 1024000,
    "mimetype": "image/jpeg"
  }
}
```

### 删除图片
- **URL**: `/image/{filename}`
- **方法**: `DELETE`
- **描述**: 删除指定图片

**查询参数**:
- `provider`: 存储提供商 (aliyun/tencent/local)

## AI 处理 API

### 身份证识别
- **URL**: `/ai/id-card-recognition`
- **方法**: `POST`
- **描述**: 识别身份证信息

**请求参数**:
```json
{
  "imageUrl": "https://example.com/idcard.jpg",
  "userId": "user_123456"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "name": "张三",
    "gender": "男",
    "nationality": "汉",
    "birthDate": "1990年1月1日",
    "address": "北京市朝阳区...",
    "idNumber": "110101199001011234"
  }
}
```

### AI人像增强
- **URL**: `/ai/portrait-enhancement`
- **方法**: `POST`
- **描述**: 增强人像照片清晰度

**请求参数**:
```json
{
  "imageUrl": "https://example.com/portrait.jpg",
  "userId": "user_123456"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "enhancedImageUrl": "https://example.com/enhanced.jpg"
  }
}
```

### 黑白照片上色
- **URL**: `/ai/image-colorization`
- **方法**: `POST`
- **描述**: 为黑白照片添加颜色

**请求参数**:
```json
{
  "imageUrl": "https://example.com/bw-photo.jpg",
  "userId": "user_123456"
}
```

### 图片无损放大
- **URL**: `/ai/image-upscaling`
- **方法**: `POST`
- **描述**: 无损放大图片

**请求参数**:
```json
{
  "imageUrl": "https://example.com/small-image.jpg",
  "userId": "user_123456",
  "scale": 2
}
```

### 图片翻译
- **URL**: `/ai/image-translation`
- **方法**: `POST`
- **描述**: 识别并翻译图片中的文字

**请求参数**:
```json
{
  "imageUrl": "https://example.com/text-image.jpg",
  "userId": "user_123456",
  "targetLanguage": "zh"
}
```

### 获取处理记录
- **URL**: `/ai/records`
- **方法**: `GET`
- **描述**: 获取AI处理记录

**查询参数**:
- `userId`: 用户ID (可选)
- `processType`: 处理类型 (可选)
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "_id": "record_id",
        "userId": "user_123456",
        "processType": "idCardRecognition",
        "status": "completed",
        "originalFile": {
          "url": "https://example.com/original.jpg"
        },
        "result": {
          "success": true,
          "data": {}
        },
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

## 系统 API

### 健康检查
- **URL**: `/health`
- **方法**: `GET`
- **描述**: 检查系统健康状态

**响应示例**:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 限制说明

### 文件上传限制
- 最大文件大小: 10MB
- 支持格式: JPG, PNG, JPEG
- 单次上传: 1个文件

### API 调用限制
- 频率限制: 100次/分钟
- 并发限制: 10个/用户
- 超时时间: 30秒

### 数据保留
- 原始图片: 处理完成后自动删除
- 处理结果: 保留7天
- 处理记录: 永久保留

## 示例代码

### JavaScript (小程序)
```javascript
// 上传图片
const uploadImage = async (filePath) => {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: 'http://localhost:3000/api/image/upload',
      filePath: filePath,
      name: 'image',
      success: (res) => {
        const data = JSON.parse(res.data)
        resolve(data)
      },
      fail: reject
    })
  })
}

// 身份证识别
const recognizeIdCard = async (imageUrl) => {
  return wx.request({
    url: 'http://localhost:3000/api/ai/id-card-recognition',
    method: 'POST',
    data: {
      imageUrl: imageUrl,
      userId: 'user_123456'
    }
  })
}
```

### cURL
```bash
# 健康检查
curl -X GET http://localhost:3000/api/health

# 身份证识别
curl -X POST http://localhost:3000/api/ai/id-card-recognition \
  -H "Content-Type: application/json" \
  -d '{
    "imageUrl": "https://example.com/idcard.jpg",
    "userId": "user_123456"
  }'
```

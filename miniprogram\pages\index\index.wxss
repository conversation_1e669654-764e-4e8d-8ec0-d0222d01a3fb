/* pages/index/index.wxss */
.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
}

/* 状态栏 */
.status-bar {
  background: transparent;
}

/* 自定义导航栏 */
.custom-navbar {
  background: transparent;
  padding: 20rpx 40rpx 40rpx;
}

.navbar-content {
  text-align: center;
}

.navbar-title {
  font-size: 44rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

.navbar-divider {
  height: 1rpx;
  background: rgba(255, 255, 255, 0.2);
  margin: 20rpx 40rpx 0;
}

/* 搜索区域 */
.search-section {
  padding: 0 40rpx 40rpx;
  background: transparent;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50rpx;
  padding: 0 30rpx;
  height: 88rpx;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
  color: #666;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  background: transparent;
  border: none;
}

.search-input::placeholder {
  color: #999;
}

.search-btn {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 44rpx;
  padding: 0 32rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.search-btn:active {
  background: rgba(255, 255, 255, 0.3);
}

.search-btn-text {
  color: white;
  font-size: 28rpx;
  font-weight: 500;
}

/* 轮播图区域 */
.banner-section {
  margin: 0 40rpx 60rpx;
}

.banner-swiper {
  height: 400rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.15);
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.6) 100%);
}

.banner-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 40rpx;
  color: white;
}

.banner-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  margin-bottom: 20rpx;
  backdrop-filter: blur(10rpx);
}

.banner-title {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.banner-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.5;
  margin-bottom: 24rpx;
  text-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.3);
}

.banner-action {
  display: flex;
  align-items: center;
}

.action-text {
  font-size: 28rpx;
  font-weight: 500;
  margin-right: 8rpx;
}

.action-arrow {
  font-size: 32rpx;
  font-weight: bold;
}

/* 内容区域背景 */
.quick-actions,
.featured-services {
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  margin-top: -32rpx;
  position: relative;
  z-index: 1;
}

.quick-actions {
  padding: 60rpx 40rpx 40rpx;
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.section-title {
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 36rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #1a1a1a;
}

.section-more {
  display: flex;
  align-items: center;
  color: #666;
  font-size: 26rpx;
}

.more-arrow {
  font-size: 32rpx;
  margin-left: 4rpx;
  color: #999;
}

/* 快捷功能网格 */
.actions-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.action-icon-wrapper {
  width: 100rpx;
  height: 100rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.action-icon {
  font-size: 40rpx;
}

.action-title {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 4rpx;
}

.action-desc {
  font-size: 20rpx;
  color: #999;
  line-height: 1.3;
}

/* 特色服务区域 */
.featured-services {
  padding: 40rpx;
  background: #fafafa;
}

.services-list {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.service-card {
  background: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.service-card:active {
  transform: translateY(-4rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}

.service-image {
  position: relative;
  height: 320rpx;
  overflow: hidden;
}

.service-image image {
  width: 100%;
  height: 100%;
}

.service-tag {
  position: absolute;
  top: 20rpx;
  left: 20rpx;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.service-content {
  padding: 32rpx;
}

.service-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #1a1a1a;
  margin-bottom: 12rpx;
}

.service-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
}

.service-features {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.feature-item {
  background: #f0f9ff;
  color: #0369a1;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
}

.service-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.action-price {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b6b;
}

.action-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.3);
}

.action-btn:active {
  transform: scale(0.95);
}

/* 底部间距 */
.bottom-spacing {
  height: 120rpx;
  background: #fafafa;
}

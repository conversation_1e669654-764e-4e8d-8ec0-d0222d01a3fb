/* pages/index/index.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* 搜索区域样式 */
.search-section {
  background-color: white;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.search-container {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
}

.search-input {
  flex: 1;
  height: 80rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  color: #333;
}

.search-input::placeholder {
  color: #999;
}

.search-btn {
  margin-left: 20rpx;
  height: 80rpx;
  padding: 0 30rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
}

/* 轮播图区域样式 */
.banner-section {
  margin: 0 30rpx 40rpx;
}

.banner-swiper {
  height: 360rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  padding: 60rpx 30rpx 30rpx;
  color: white;
}

.banner-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.banner-desc {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 功能区域样式 */
.features-section {
  margin: 0 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-left: 10rpx;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.feature-placeholder {
  background-color: white;
  border-radius: 16rpx;
  padding: 60rpx 30rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

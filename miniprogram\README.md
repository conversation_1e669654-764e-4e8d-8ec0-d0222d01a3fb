# AI证件照制作微信小程序

## 项目结构

```
miniprogram/
├── app.js                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.wxss              # 全局样式文件
├── project.config.json   # 项目配置文件
├── sitemap.json          # 站点地图配置
├── pages/                # 页面目录
│   └── index/           # 首页
│       ├── index.js     # 页面逻辑
│       ├── index.json   # 页面配置
│       ├── index.wxml   # 页面结构
│       └── index.wxss   # 页面样式
├── images/              # 图片资源目录
├── utils/               # 工具类目录
│   └── api.js          # API调用工具
└── README.md           # 说明文档
```

## 当前功能

### 首页 (pages/index)
- ✅ 搜索条 + 搜索按钮
- ✅ 轮播图组件
- ✅ 基础页面结构
- ✅ 响应式布局
- ✅ 下拉刷新功能

### 已实现的特性
- 现代化UI设计
- 搜索功能框架
- 轮播图自动播放
- API调用工具类
- 错误处理机制
- 加载状态管理

## 开发说明

### 1. 开发环境
- 微信开发者工具
- Node.js 16.x+
- 后端服务运行在 http://localhost:3000

### 2. 配置说明
- 修改 `project.config.json` 中的 `appid` 为您的小程序AppID
- 修改 `app.js` 中的 `baseUrl` 为实际的后端API地址

### 3. 图片资源
需要添加以下图片到 `images/` 目录：
- `home.png` / `home-active.png` - 底部导航图标
- `banner1.jpg` / `banner2.jpg` / `banner3.jpg` - 轮播图
- `share.jpg` - 分享图片

### 4. 下一步开发计划
- [ ] 添加更多底部导航菜单
- [ ] 实现证件照制作功能页面
- [ ] 添加用户登录功能
- [ ] 实现图片上传和处理
- [ ] 添加历史记录页面
- [ ] 完善错误处理和用户体验

## API接口

小程序通过 `utils/api.js` 与后端服务通信，支持以下功能：
- 健康检查
- AI图片处理服务
- 用户认证
- 配置管理
- 用户作品管理
- 数据统计

## 注意事项

1. **域名配置**: 在小程序后台配置服务器域名白名单
2. **HTTPS要求**: 生产环境必须使用HTTPS协议
3. **图片优化**: 建议对图片进行压缩优化
4. **性能优化**: 注意页面加载性能和用户体验
5. **错误处理**: 完善网络请求的错误处理机制

## 开发调试

1. 使用微信开发者工具打开 `miniprogram` 目录
2. 确保后端服务正在运行
3. 在开发者工具中启用"不校验合法域名"选项
4. 开始开发和调试

// utils/api.js - API调用工具类

const app = getApp()

/**
 * 封装wx.request
 */
function request(options) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${app.globalData.baseUrl}${options.url}`,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          reject(new Error(`请求失败: ${res.statusCode}`))
        }
      },
      fail: (err) => {
        reject(err)
      }
    })
  })
}

/**
 * GET请求
 */
function get(url, data = {}) {
  return request({
    url,
    method: 'GET',
    data
  })
}

/**
 * POST请求
 */
function post(url, data = {}) {
  return request({
    url,
    method: 'POST',
    data
  })
}

/**
 * 上传文件
 */
function uploadFile(filePath, fileName = 'image') {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: `${app.globalData.baseUrl}/image/upload`,
      filePath: filePath,
      name: fileName,
      success: (res) => {
        try {
          const data = JSON.parse(res.data)
          resolve(data)
        } catch (e) {
          reject(new Error('响应数据解析失败'))
        }
      },
      fail: reject
    })
  })
}

/**
 * 显示加载提示
 */
function showLoading(title = '加载中...') {
  wx.showLoading({
    title,
    mask: true
  })
}

/**
 * 隐藏加载提示
 */
function hideLoading() {
  wx.hideLoading()
}

/**
 * 显示成功提示
 */
function showSuccess(title) {
  wx.showToast({
    title,
    icon: 'success',
    duration: 2000
  })
}

/**
 * 显示错误提示
 */
function showError(title) {
  wx.showToast({
    title,
    icon: 'none',
    duration: 2000
  })
}

/**
 * API接口定义
 */
const API = {
  // 健康检查
  healthCheck: () => get('/health'),
  
  // AI服务相关
  createIdPhoto: (data) => post('/ai/id-photo-creation', data),
  enhancePortrait: (data) => post('/ai/portrait-enhancement', data),
  colorizeImage: (data) => post('/ai/image-colorization', data),
  upscaleImage: (data) => post('/ai/image-upscaling', data),
  translateImage: (data) => post('/ai/image-translation', data),
  
  // 用户认证相关
  wechatLogin: (data) => post('/auth/wechat-login', data),
  getUserInfo: () => get('/auth/user-info'),
  
  // 获取配置信息
  getConfig: () => get('/config'),
  
  // 获取证件照尺寸规格
  getPhotoSizes: () => get('/photo-sizes'),
  
  // 用户作品相关
  getUserWorks: (params) => get('/user-works', params),
  saveUserWork: (data) => post('/user-works', data),
  
  // 统计数据
  getExploreCounts: () => get('/statistics/explore-counts')
}

module.exports = {
  request,
  get,
  post,
  uploadFile,
  showLoading,
  hideLoading,
  showSuccess,
  showError,
  API
}

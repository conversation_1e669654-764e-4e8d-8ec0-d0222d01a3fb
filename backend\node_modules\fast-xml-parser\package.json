{"name": "fast-xml-parser", "version": "4.2.5", "description": "Validate XML, Parse XML, Build XML without C/C++ based libraries", "main": "./src/fxp.js", "scripts": {"test": "nyc --reporter=lcov --reporter=text jasmine spec/*spec.js", "unit": "jasmine", "coverage": "nyc report --reporter html --reporter text -t .nyc_output --report-dir .nyc_output/summary", "perf": "node ./benchmark/perfTest3.js", "lint": "eslint src/*.js spec/*.js", "bundle": "webpack --config webpack-prod.config.js", "prettier": "prettier --write src/**/*.js", "publish-please": "publish-please", "checkReadiness": "publish-please --dry-run"}, "bin": {"fxparser": "./src/cli/cli.js"}, "repository": {"type": "git", "url": "https://github.com/NaturalIntelligence/fast-xml-parser"}, "keywords": ["fast", "xml", "json", "parser", "xml2js", "x2js", "xml2json", "js", "cli", "validator", "validate", "transformer", "assert", "js2xml", "json2xml", "html"], "author": "<PERSON><PERSON> (https://amitkumargupta.work/)", "license": "MIT", "devDependencies": {"@babel/core": "^7.13.10", "@babel/plugin-transform-runtime": "^7.13.10", "@babel/preset-env": "^7.13.10", "@babel/register": "^7.13.8", "babel-loader": "^8.2.2", "cytorus": "^0.2.9", "eslint": "^8.3.0", "he": "^1.2.0", "jasmine": "^3.6.4", "nyc": "^15.1.0", "prettier": "^1.19.1", "publish-please": "^5.5.2", "webpack": "^5.64.4", "webpack-cli": "^4.9.1"}, "typings": "src/fxp.d.ts", "funding": [{"type": "paypal", "url": "https://paypal.me/naturalintelligence"}, {"type": "github", "url": "https://github.com/sponsors/NaturalIntelligence"}], "dependencies": {"strnum": "^1.0.5"}}
const COS = require('cos-nodejs-sdk-v5');
const fs = require('fs');
const path = require('path');

// 测试腾讯云连接
async function testTencentConnection(config) {
  try {
    if (!config.secretId || !config.secretKey) {
      return {
        success: false,
        message: '请配置腾讯云SecretId和SecretKey'
      };
    }

    // 测试COS连接
    if (config.cos && config.cos.bucket) {
      const cos = new COS({
        SecretId: config.secretId,
        SecretKey: config.secretKey
      });

      await new Promise((resolve, reject) => {
        cos.getBucket({
          Bucket: config.cos.bucket,
          Region: config.cos.region
        }, (err, data) => {
          if (err) reject(err);
          else resolve(data);
        });
      });
    }

    return {
      success: true,
      message: '腾讯云连接测试成功'
    };
  } catch (error) {
    return {
      success: false,
      message: `腾讯云连接测试失败: ${error.message}`
    };
  }
}

// 上传文件到腾讯云COS
async function uploadToTencent(filePath, filename, config) {
  try {
    if (!config.cos || !config.cos.bucket) {
      throw new Error('COS配置不完整');
    }

    const cos = new COS({
      SecretId: config.secretId,
      SecretKey: config.secretKey
    });

    const result = await new Promise((resolve, reject) => {
      cos.putObject({
        Bucket: config.cos.bucket,
        Region: config.cos.region,
        Key: filename,
        Body: fs.createReadStream(filePath)
      }, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });

    const url = `https://${config.cos.bucket}.cos.${config.cos.region}.myqcloud.com/${filename}`;
    
    return {
      success: true,
      url: url,
      name: filename
    };
  } catch (error) {
    console.error('腾讯云上传失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

// 从腾讯云COS删除文件
async function deleteFromTencent(filename, config) {
  try {
    if (!config.cos || !config.cos.bucket) {
      throw new Error('COS配置不完整');
    }

    const cos = new COS({
      SecretId: config.secretId,
      SecretKey: config.secretKey
    });

    await new Promise((resolve, reject) => {
      cos.deleteObject({
        Bucket: config.cos.bucket,
        Region: config.cos.region,
        Key: filename
      }, (err, data) => {
        if (err) reject(err);
        else resolve(data);
      });
    });
    
    return {
      success: true,
      message: '文件删除成功'
    };
  } catch (error) {
    console.error('腾讯云删除失败:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

module.exports = {
  testTencentConnection,
  uploadToTencent,
  deleteFromTencent
};

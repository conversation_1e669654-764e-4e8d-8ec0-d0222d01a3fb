exports.fetch = isFunction(global.fetch) && isFunction(global.ReadableStream);

exports.writableStream = isFunction(global.WritableStream);

exports.abortController = isFunction(global.AbortController);

exports.blobConstructor = false;
try {
  new Blob([new ArrayBuffer(1)]);
  exports.blobConstructor = true;
} catch (e) {}

// The xhr request to example.com may violate some restrictive CSP configurations,
// so if we're running in a browser that supports `fetch`, avoid calling getXHR()
// and assume support for certain features below.
var xhr;
function getXHR() {
  // Cache the xhr value
  if (xhr !== undefined) return xhr;

  if (global.XMLHttpRequest) {
    xhr = new global.XMLHttpRequest();
    // If XDomainRequest is available (ie only, where xhr might not work
    // cross domain), use the page location. Otherwise use example.com
    // Note: this doesn't actually make an http request.
    try {
      xhr.open('GET', global.XDomainRequest ? '/' : 'https://example.com');
    } catch (e) {
      xhr = null;
    }
  } else {
    // Service workers don't have XHR
    xhr = null;
  }
  return xhr;
}

function checkTypeSupport(type) {
  var xhr = getXHR();
  if (!xhr) return false;
  try {
    xhr.responseType = type;
    return xhr.responseType === type;
  } catch (e) {}
  return false;
}

// For some strange reason, Safari 7.0 reports typeof global.ArrayBuffer === 'object'.
// Safari 7.1 appears to have fixed this bug.
var haveArrayBuffer = typeof global.ArrayBuffer !== 'undefined';
var haveSlice = haveArrayBuffer && isFunction(global.ArrayBuffer.prototype.slice);

// If fetch is supported, then arraybuffer will be supported too. Skip calling
// checkTypeSupport(), since that calls getXHR().
exports.arraybuffer = exports.fetch || (haveArrayBuffer && checkTypeSupport('arraybuffer'));

// These next two tests unavoidably show warnings in Chrome. Since fetch will always
// be used if it's available, just return false for these to avoid the warnings.
exports.msstream = !exports.fetch && haveSlice && checkTypeSupport('ms-stream');
exports.mozchunkedarraybuffer = !exports.fetch && haveArrayBuffer && checkTypeSupport('moz-chunked-arraybuffer');

// If fetch is supported, then overrideMimeType will be supported too. Skip calling
// getXHR().
exports.overrideMimeType = exports.fetch || (getXHR() ? isFunction(getXHR().overrideMimeType) : false);

exports.vbArray = isFunction(global.VBArray);

function isFunction(value) {
  return typeof value === 'function';
}

xhr = null; // Help gc

# [fast-xml-parser](https://www.npmjs.com/package/fast-xml-parser)
[![Backers on Open Collective](https://opencollective.com/fast-xml-parser/backers/badge.svg)](#backers) [![Sponsors on Open Collective](https://opencollective.com/fast-xml-parser/sponsors/badge.svg)](#sponsors) [![Known Vulnerabilities](https://snyk.io/test/github/naturalintelligence/fast-xml-parser/badge.svg)](https://snyk.io/test/github/naturalintelligence/fast-xml-parser)
[![NPM quality][quality-image]][quality-url]
[![Coverage Status](https://coveralls.io/repos/github/NaturalIntelligence/fast-xml-parser/badge.svg?branch=master)](https://coveralls.io/github/NaturalIntelligence/fast-xml-parser?branch=master)
[<img src="https://img.shields.io/badge/Try-me-blue.svg?colorA=FFA500&colorB=0000FF" alt="Try me"/>](https://naturalintelligence.github.io/fast-xml-parser/)
[![NPM total downloads](https://img.shields.io/npm/dt/fast-xml-parser.svg)](https://npm.im/fast-xml-parser)

[quality-image]: http://npm.packagequality.com/shield/fast-xml-parser.svg?style=flat-square
[quality-url]: http://packagequality.com/#?package=fast-xml-parser


Validate XML, Parse XML to JS Object, or Build XML from JS Object without C/C++ based libraries and no callback.

> Looking for maintainers

Support this project by becoming a **Sponsor**. 

Click on Sponsor button above or
<a href="https://opencollective.com/fast-xml-parser/donate" target="_blank">
  <img src="https://opencollective.com/fast-xml-parser/donate/<EMAIL>?color=blue" width=200 />
</a>
<a href="https://paypal.me/naturalintelligence"> <img src="static/img/support_paypal.svg" alt="Stubmatic donate button" width="200"/></a>


### Sponsors

<a href="https://github.com/getsentry" target="_blank"><img src="https://user-images.githubusercontent.com/7692328/204701653-ce369b29-6fdb-48f2-9848-593d04628a2b.png" width="60px"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/0/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/0/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/1/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/1/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/2/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/2/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/3/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/3/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/4/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/4/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/5/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/5/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/6/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/6/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/7/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/7/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/8/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/8/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/9/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/9/avatar.svg"></a>


Check [ThankYouBackers](https://github.com/NaturalIntelligence/ThankYouBackers) for our supporters

## Users

<a href="https://github.com/renovatebot/renovate" title="renovate" ><img src="https://avatars1.githubusercontent.com/u/38656520" width="60px" ></a>
<a href="https://vmware.com/" title="vmware" > <img src="https://avatars0.githubusercontent.com/u/473334" width="60px" ></a>
<a href="https://opensource.microsoft.com/" title="microsoft" > <img src="https://avatars0.githubusercontent.com/u/6154722" width="60px" ></a>
<a href="http://ibm.github.io/" title="IBM" > <img src="https://avatars2.githubusercontent.com/u/1459110" width="60px" ></a>
<a href="http://www.smartbear.com" title="SmartBear Software" > <img src="https://avatars2.githubusercontent.com/u/1644671" width="60px" ></a>
<a href="http://nasa.github.io/" title="NASA" > <img src="https://avatars0.githubusercontent.com/u/848102" width="60px" ></a>
<a href="https://github.com/prettier" title="Prettier" > <img src="https://avatars0.githubusercontent.com/u/25822731" width="60px" ></a>
<a href="http://brain.js.org/" title="brain.js" > <img src="https://avatars2.githubusercontent.com/u/23732838" width="60px" ></a>
<a href="https://github.com/aws" title="AWS SDK" > <img src="https://avatars.githubusercontent.com/u/2232217" width="60px" ></a>
<a href="http://www.fda.gov/" title="Food and Drug Administration " > <img src="https://avatars2.githubusercontent.com/u/6471964" width="60px" ></a>
<a href="http://www.magento.com/" title="Magento" > <img src="https://avatars2.githubusercontent.com/u/168457" width="60px" ></a>
<a href="https://github.com/SAP" title="SAP" > <img src="https://user-images.githubusercontent.com/7692328/204835214-d9d25b58-e3df-408d-87a3-c7d36b578ee4.png" width="60px" ></a>
<a href="https://github.com/postmanlabs" title="postman" > <img src="https://user-images.githubusercontent.com/7692328/204835529-e9e290ad-696a-49ad-9d34-08e955704715.png" width="60px" ></a>
<a href="https://github.com/react-native-community" title="React Native Community" > <img src="https://avatars.githubusercontent.com/u/20269980?v=4" width="60px" ></a>

Check the list of all known users [here](./USERs.md);

<small>The list of users is collected either from the list published by Github, cummunicated directly through mails/chat , or from other resources. If you feel that your name in the above list is incorrectly published or you're not the user of this library anymore then you can inform us to remove it. We'll do the necessary changes ASAP.</small>

## Main Features

<img align="right" src="static/img/fxp_logo.png" width="180px" alt="FXP logo"/>

* Validate XML data syntactically
* Parse XML to JS Object
* Build XML from JS Object
* Works with node packages, in browser, and in CLI (press try me button above for demo)
* Faster than any other pure JS implementation.
* It can handle big files (tested up to 100mb).
* Controlled parsing using various options
* XML Entities, HTML entities, and DOCTYPE entites are supported.
* unpaired tags (Eg `<br>` in HTML), stop nodes (Eg `<script>` in HTML) are supported.
* You can restore almost same XML from JSON
* Supports comments
* It can preserve Order of tags in JS object
* You can control if a single tag should be parsed into array.
* Supports parsing of PI (Processing Instruction) tags with XML declaration tags
* And many more other features.

## How to use

To use as package dependency
`$ npm install fast-xml-parser` 
or 
`$ yarn add fast-xml-parser`

To use as system command
`$ npm install fast-xml-parser -g` 

To use it on a **webpage** include it from a [CDN](https://cdnjs.com/libraries/fast-xml-parser)

**Example**

As CLI command
```bash
$ fxparser some.xml
```

In a node js project
```js
const { XMLParser, XMLBuilder, XMLValidator} = require("fast-xml-parser");

const parser = new XMLParser();
let jObj = parser.parse(XMLdata);

const builder = new XMLBuilder();
const xmlContent = builder.build(jObj);
```

In a HTML page
```html
<script src="path/to/fxp.min.js"></script>
:
<script>
  const parser = new fxparser.XMLParser();
  parser.parse(xmlContent);
</script>
```

Check lib folder for different browser bundles

| Bundle Name        | Size |
| ------------------ | ---- |
| fxbuilder.min.js   | 5.2K |
| fxparser.js        | 50K  |
| fxparser.min.js    | 17K  |
| fxp.min.js         | 22K  |
| fxvalidator.min.js | 5.7K |

### Documents
**v3**
* [documents](./docs/v3/docs.md)

**v4**
1. [GettingStarted.md](./docs/v4/1.GettingStarted.md)
2.  [XML Parser](./docs/v4/2.XMLparseOptions.md)
3.  [XML Builder](./docs/v4/3.XMLBuilder.md)
4.  [XML Validator](./docs/v4/4.XMLValidator.md)
5.  [Entities](./docs/v4/5.Entities.md)
6.  [HTML Document Parsing](./docs/v4/6.HTMLParsing.md)
7. [PI Tag processing](./docs/v4/7.PITags.md)
## Performance

### XML Parser

![](./docs/imgs/XMLParser_v4.png)
* Y-axis: requests per second
* X-axis: File size

**Large files**
![](./docs/imgs/XMLParser_large_v4.png)
* Y-axis: requests per second
* X-axis: File size 
### XML Builder

![](./docs/imgs/XMLBuilder_v4.png)
* Y-axis: requests per second

<small>negative means error</small>

[![](static/img/ni_ads_ads.gif)](https://github.com/NaturalIntelligence/ads/)

## Our other projects and research you must try

* **[BigBit standard](https://github.com/amitguptagwl/bigbit)** : 
  * Single text encoding to replace UTF-8, UTF-16, UTF-32 and more with less memory.
  * Single Numeric datatype alternative of integer, float, double, long, decimal and more without precision loss.
* **[Cytorus](https://github.com/NaturalIntelligence/cytorus)**:  Be specific and flexible while running E2E tests.
  * Run tests only for a particular User Story
  * Run tests for a route or from a route
  * Customizable reporting
  * Central dashboard for better monitoring
  * Options to integrate E2E tests with Jira, Github etc using Central dashboard `Tian`.
* **[Stubmatic](https://github.com/NaturalIntelligence/Stubmatic)** : Create fake webservices, DynamoDB or S3 servers, Manage fake/mock stub data, Or fake any HTTP(s) call.


## Supporters
### Contributors

This project exists thanks to [all](graphs/contributors) the people who contribute. [[Contribute](docs/CONTRIBUTING.md)].
<!-- <a href="graphs/contributors"><img src="https://opencollective.com/fast-xml-parser/contributors.svg?width=890&button=false" /></a> -->
<!--
### Lead Maintainers
![Amit Gupta](https://avatars1.githubusercontent.com/u/7692328?s=100&v=4)
[![Vohmyanin Sergey Vasilevich](https://avatars3.githubusercontent.com/u/783335?s=100&v=4)](https://github.com/Delagen)

### All Contributors -->
<a href="graphs/contributors"><img src="https://opencollective.com/fast-xml-parser/contributors.svg?width=890&button=false" /></a>

### Backers

Thank you to all our backers! 🙏 [[Become a backer](https://opencollective.com/fast-xml-parser#backer)]

<a href="https://opencollective.com/fast-xml-parser#backers" target="_blank"><img src="https://opencollective.com/fast-xml-parser/backers.svg?width=890"></a>



# License
* MIT License

![Donate $5](static/img/donation_quote.png)

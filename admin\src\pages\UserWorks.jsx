import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Image,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  message,
  Tooltip,
  Statistic,
  Row,
  Col
} from 'antd';
import {
  EyeOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  DeleteOutlined,
  HeartOutlined,
  HeartFilled
} from '@ant-design/icons';
import { userWorksAPI, statisticsAPI } from '../services/api';

const { RangePicker } = DatePicker;
const { Option } = Select;

const UserWorks = () => {
  const [works, setWorks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [filters, setFilters] = useState({
    type: '',
    isFavorite: '',
    dateRange: null
  });
  const [stats, setStats] = useState({
    total: 0,
    favorite: 0,
    public: 0,
    totalDownloads: 0,
    totalShares: 0
  });

  // 获取作品列表
  const fetchWorks = async (params = {}) => {
    setLoading(true);
    try {
      const queryParams = {
        pageNum: pagination.current,
        pageSize: pagination.pageSize,
        ...filters,
        ...params
      };

      if (filters.dateRange) {
        queryParams.startDate = filters.dateRange[0].format('YYYY-MM-DD');
        queryParams.endDate = filters.dateRange[1].format('YYYY-MM-DD');
      }

      const response = await userWorksAPI.getList(queryParams);
      
      if (response.success) {
        setWorks(response.data.records);
        setPagination(prev => ({
          ...prev,
          total: response.data.total
        }));
      }
    } catch (error) {
      message.error('获取作品列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const response = await statisticsAPI.getOverview();
      if (response.success) {
        setStats(response.data.works);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  useEffect(() => {
    fetchWorks();
    fetchStats();
  }, [pagination.current, pagination.pageSize]);

  // 删除作品
  const handleDelete = async (id) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个作品吗？此操作不可恢复。',
      onOk: async () => {
        try {
          await userWorksAPI.delete(id);
          message.success('删除成功');
          fetchWorks();
          fetchStats();
        } catch (error) {
          message.error('删除失败');
        }
      }
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '预览',
      dataIndex: 'processedImage',
      key: 'preview',
      width: 80,
      render: (processedImage) => (
        <Image
          width={50}
          height={50}
          src={processedImage?.url}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      )
    },
    {
      title: '作品名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type) => {
        const typeMap = {
          idPhoto: { text: '证件照', color: 'blue' },
          enhanced: { text: '人像增强', color: 'green' },
          colorized: { text: '图片上色', color: 'orange' },
          upscaled: { text: '图片放大', color: 'purple' },
          translated: { text: '图片翻译', color: 'cyan' }
        };
        const config = typeMap[type] || { text: type, color: 'default' };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
      key: 'userId',
      width: 120,
      ellipsis: true
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (_, record) => (
        <Space>
          {record.isFavorite && (
            <Tooltip title="已收藏">
              <HeartFilled style={{ color: '#ff4d4f' }} />
            </Tooltip>
          )}
          {record.isPublic && (
            <Tag color="green">公开</Tag>
          )}
        </Space>
      )
    },
    {
      title: '下载/分享',
      key: 'stats',
      width: 100,
      render: (_, record) => (
        <Space direction="vertical" size={0}>
          <span>下载: {record.downloadCount}</span>
          <span>分享: {record.shareCount}</span>
        </Space>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date) => new Date(date).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="下载">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={() => handleDownload(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record._id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 查看详情
  const handleViewDetail = (record) => {
    Modal.info({
      title: '作品详情',
      width: 600,
      content: (
        <div>
          <Row gutter={16}>
            <Col span={12}>
              <p><strong>作品名称:</strong> {record.name}</p>
              <p><strong>类型:</strong> {record.type}</p>
              <p><strong>用户ID:</strong> {record.userId}</p>
              <p><strong>创建时间:</strong> {new Date(record.createdAt).toLocaleString()}</p>
            </Col>
            <Col span={12}>
              <p><strong>下载次数:</strong> {record.downloadCount}</p>
              <p><strong>分享次数:</strong> {record.shareCount}</p>
              <p><strong>是否收藏:</strong> {record.isFavorite ? '是' : '否'}</p>
              <p><strong>是否公开:</strong> {record.isPublic ? '是' : '否'}</p>
            </Col>
          </Row>
          <div style={{ marginTop: 16 }}>
            <p><strong>处理后图片:</strong></p>
            <Image src={record.processedImage?.url} style={{ maxWidth: '100%' }} />
          </div>
        </div>
      )
    });
  };

  // 下载作品
  const handleDownload = (record) => {
    const link = document.createElement('a');
    link.href = record.processedImage?.url;
    link.download = record.processedImage?.filename || `${record.name}.jpg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // 搜索
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchWorks();
  };

  // 重置搜索
  const handleReset = () => {
    setFilters({
      type: '',
      isFavorite: '',
      dateRange: null
    });
    setPagination(prev => ({ ...prev, current: 1 }));
    setTimeout(() => fetchWorks(), 0);
  };

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic title="总作品数" value={stats.total} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="收藏作品" value={stats.favorite} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总下载量" value={stats.totalDownloads} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="总分享量" value={stats.totalShares} />
          </Card>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form layout="inline">
          <Form.Item label="作品类型">
            <Select
              style={{ width: 120 }}
              value={filters.type}
              onChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
              allowClear
            >
              <Option value="idPhoto">证件照</Option>
              <Option value="enhanced">人像增强</Option>
              <Option value="colorized">图片上色</Option>
              <Option value="upscaled">图片放大</Option>
              <Option value="translated">图片翻译</Option>
            </Select>
          </Form.Item>
          <Form.Item label="收藏状态">
            <Select
              style={{ width: 100 }}
              value={filters.isFavorite}
              onChange={(value) => setFilters(prev => ({ ...prev, isFavorite: value }))}
              allowClear
            >
              <Option value="true">已收藏</Option>
              <Option value="false">未收藏</Option>
            </Select>
          </Form.Item>
          <Form.Item label="创建时间">
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => setFilters(prev => ({ ...prev, dateRange: dates }))}
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleSearch}>
                搜索
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 作品表格 */}
      <Card title="用户作品管理">
        <Table
          columns={columns}
          dataSource={works}
          rowKey="_id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({ ...prev, current: page, pageSize }));
            }
          }}
        />
      </Card>
    </div>
  );
};

export default UserWorks;

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  message, 
  Tabs, 
  Space,
  Typography,
  Divider,
  Switch,
  Select,
  InputNumber,
  Tag
} from 'antd';
import {
  CloudOutlined,
  SaveOutlined,
  CheckCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { configAPI } from '../services/api';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;

const ConfigManagement = () => {
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState({});
  const [config, setConfig] = useState(null);
  const [form] = Form.useForm();

  const loadConfig = async () => {
    setLoading(true);
    try {
      const response = await configAPI.getConfig();
      if (response.success) {
        setConfig(response.data);
        form.setFieldsValue(response.data);
      }
    } catch (error) {
      message.error('加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadConfig();
  }, []);

  const handleSaveAliyun = async (values) => {
    setLoading(true);
    try {
      const response = await configAPI.updateAliyunConfig(values);
      if (response.success) {
        message.success('阿里云配置保存成功');
        loadConfig();
      }
    } catch (error) {
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveTencent = async (values) => {
    setLoading(true);
    try {
      const response = await configAPI.updateTencentConfig(values);
      if (response.success) {
        message.success('腾讯云配置保存成功');
        loadConfig();
      }
    } catch (error) {
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveAIServices = async (values) => {
    setLoading(true);
    try {
      const response = await configAPI.updateAIServicesConfig({ aiServices: values });
      if (response.success) {
        message.success('AI服务配置保存成功');
        loadConfig();
      }
    } catch (error) {
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSystem = async (values) => {
    setLoading(true);
    try {
      const response = await configAPI.updateSystemConfig({ system: values });
      if (response.success) {
        message.success('系统配置保存成功');
        loadConfig();
      }
    } catch (error) {
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async (provider) => {
    setTestLoading({ ...testLoading, [provider]: true });
    try {
      const response = await configAPI.testConnection(provider);
      if (response.success) {
        message.success(response.message);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      message.error('测试连接失败');
    } finally {
      setTestLoading({ ...testLoading, [provider]: false });
    }
  };

  if (!config) {
    return <div>加载中...</div>;
  }

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>配置管理</Title>
        <Paragraph type="secondary">
          配置云服务密钥和系统参数
        </Paragraph>
      </div>

      <Tabs defaultActiveKey="aliyun" type="card">
        <TabPane tab={
          <Space>
            <CloudOutlined />
            阿里云配置
          </Space>
        } key="aliyun">
          <Card>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSaveAliyun}
              initialValues={config.aliyun}
            >
              <Form.Item
                label="Access Key ID"
                name={['aliyun', 'accessKeyId']}
                rules={[{ required: true, message: '请输入Access Key ID' }]}
              >
                <Input placeholder="请输入阿里云Access Key ID" />
              </Form.Item>

              <Form.Item
                label="Access Key Secret"
                name={['aliyun', 'accessKeySecret']}
                rules={[{ required: true, message: '请输入Access Key Secret' }]}
              >
                <Input.Password placeholder="请输入阿里云Access Key Secret" />
              </Form.Item>

              <Form.Item
                label="地域"
                name={['aliyun', 'region']}
              >
                <Select placeholder="选择地域">
                  <Select.Option value="cn-hangzhou">华东1（杭州）</Select.Option>
                  <Select.Option value="cn-shanghai">华东2（上海）</Select.Option>
                  <Select.Option value="cn-beijing">华北2（北京）</Select.Option>
                  <Select.Option value="cn-shenzhen">华南1（深圳）</Select.Option>
                </Select>
              </Form.Item>

              <Divider>OSS配置</Divider>

              <Form.Item
                label="OSS Bucket"
                name={['aliyun', 'oss', 'bucket']}
              >
                <Input placeholder="请输入OSS存储桶名称" />
              </Form.Item>

              <Form.Item
                label="OSS地域"
                name={['aliyun', 'oss', 'region']}
              >
                <Input placeholder="如: oss-cn-hangzhou" />
              </Form.Item>

              <Form.Item
                label="OSS Endpoint"
                name={['aliyun', 'oss', 'endpoint']}
              >
                <Input placeholder="如: https://oss-cn-hangzhou.aliyuncs.com" />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    icon={<SaveOutlined />}
                    loading={loading}
                  >
                    保存配置
                  </Button>
                  <Button
                    icon={<CheckCircleOutlined />}
                    loading={testLoading.aliyun}
                    onClick={() => handleTestConnection('aliyun')}
                  >
                    测试连接
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab={
          <Space>
            <CloudOutlined />
            腾讯云配置
          </Space>
        } key="tencent">
          <Card>
            <Form
              layout="vertical"
              onFinish={handleSaveTencent}
              initialValues={config.tencent}
            >
              <Form.Item
                label="Secret ID"
                name={['tencent', 'secretId']}
              >
                <Input placeholder="请输入腾讯云Secret ID" />
              </Form.Item>

              <Form.Item
                label="Secret Key"
                name={['tencent', 'secretKey']}
              >
                <Input.Password placeholder="请输入腾讯云Secret Key" />
              </Form.Item>

              <Divider>COS配置</Divider>

              <Form.Item
                label="COS Bucket"
                name={['tencent', 'cos', 'bucket']}
              >
                <Input placeholder="请输入COS存储桶名称" />
              </Form.Item>

              <Form.Item
                label="COS地域"
                name={['tencent', 'cos', 'region']}
              >
                <Select placeholder="选择地域">
                  <Select.Option value="ap-beijing">北京</Select.Option>
                  <Select.Option value="ap-shanghai">上海</Select.Option>
                  <Select.Option value="ap-guangzhou">广州</Select.Option>
                  <Select.Option value="ap-chengdu">成都</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    icon={<SaveOutlined />}
                    loading={loading}
                  >
                    保存配置
                  </Button>
                  <Button
                    icon={<CheckCircleOutlined />}
                    loading={testLoading.tencent}
                    onClick={() => handleTestConnection('tencent')}
                  >
                    测试连接
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab={
          <Space>
            <SettingOutlined />
            AI服务配置
          </Space>
        } key="ai">
          <Card>
            <Form
              layout="vertical"
              onFinish={handleSaveAIServices}
              initialValues={config.aiServices}
            >
              {Object.entries(config.aiServices || {}).map(([key, service]) => (
                <div key={key} style={{ marginBottom: 16 }}>
                  <Title level={5}>{getServiceName(key)}</Title>
                  <Form.Item
                    label="启用服务"
                    name={[key, 'enabled']}
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Form.Item
                    label="API端点"
                    name={[key, 'endpoint']}
                  >
                    <Input placeholder="API端点地址" />
                  </Form.Item>
                  <Divider />
                </div>
              ))}

              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  icon={<SaveOutlined />}
                  loading={loading}
                >
                  保存AI服务配置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        <TabPane tab={
          <Space>
            <SettingOutlined />
            系统配置
          </Space>
        } key="system">
          <Card>
            <Form
              layout="vertical"
              onFinish={handleSaveSystem}
              initialValues={config.system}
            >
              <Form.Item
                label="最大文件大小 (字节)"
                name={['system', 'maxFileSize']}
              >
                <InputNumber 
                  style={{ width: '100%' }}
                  placeholder="10485760"
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                />
              </Form.Item>

              <Form.Item
                label="允许的文件类型"
                name={['system', 'allowedFileTypes']}
              >
                <Select mode="tags" placeholder="选择或输入文件类型">
                  <Select.Option value="image/jpeg">image/jpeg</Select.Option>
                  <Select.Option value="image/png">image/png</Select.Option>
                  <Select.Option value="image/jpg">image/jpg</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item
                label="存储提供商"
                name={['system', 'storageProvider']}
              >
                <Select placeholder="选择存储提供商">
                  <Select.Option value="aliyun">阿里云</Select.Option>
                  <Select.Option value="tencent">腾讯云</Select.Option>
                  <Select.Option value="both">双云备份</Select.Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Button 
                  type="primary" 
                  htmlType="submit" 
                  icon={<SaveOutlined />}
                  loading={loading}
                >
                  保存系统配置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

const getServiceName = (key) => {
  const names = {
    idPhotoCreation: '证件照制作',
    portraitEnhancement: 'AI人像变清晰',
    imageColorization: '黑白照片上色',
    imageUpscaling: '图片无损放大',
    imageTranslation: '图片翻译',
    portraitClarification: '人像变清晰'
  };
  return names[key] || key;
};

export default ConfigManagement;

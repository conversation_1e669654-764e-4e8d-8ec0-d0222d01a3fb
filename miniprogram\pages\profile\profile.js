// pages/profile/profile.js
const { API, showLoading, hideLoading, showError, showSuccess } = require('../../utils/api')

Page({
  /**
   * 页面的初始数据
   */
  data: {
    userInfo: {},
    stats: {
      totalWorks: 0,
      totalDays: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('个人中心页面加载')
    this.loadUserInfo()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    console.log('个人中心页面显示')
    this.loadUserStats()
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    // 从本地存储获取用户信息
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({
        userInfo: userInfo
      })
    }
  },

  /**
   * 加载用户统计数据
   */
  async loadUserStats() {
    if (!this.data.userInfo.openid) {
      return
    }

    try {
      showLoading('加载中...')
      // TODO: 调用后端API获取用户统计数据
      // const result = await API.getUserStats(this.data.userInfo.openid)

      // 模拟数据
      const stats = {
        totalWorks: Math.floor(Math.random() * 50),
        totalDays: Math.floor(Math.random() * 30) + 1
      }

      this.setData({
        stats: stats
      })
    } catch (error) {
      console.error('加载用户统计失败:', error)
      showError('加载统计数据失败')
    } finally {
      hideLoading()
    }
  },

  /**
   * 用户登录
   */
  onLogin() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        console.log('获取用户信息成功:', res.userInfo)

        // 保存用户信息到本地存储
        wx.setStorageSync('userInfo', res.userInfo)

        this.setData({
          userInfo: res.userInfo
        })

        showSuccess('登录成功')

        // TODO: 调用后端API进行微信登录
        this.wechatLogin(res.userInfo)
      },
      fail: (err) => {
        console.error('获取用户信息失败:', err)
        showError('登录失败')
      }
    })
  },

  /**
   * 微信登录
   */
  async wechatLogin(userInfo) {
    try {
      wx.login({
        success: async (loginRes) => {
          if (loginRes.code) {
            // TODO: 调用后端API进行登录
            // const result = await API.wechatLogin({
            //   code: loginRes.code,
            //   userInfo: userInfo
            // })
            console.log('微信登录code:', loginRes.code)
          }
        }
      })
    } catch (error) {
      console.error('微信登录失败:', error)
    }
  },

  /**
   * 菜单点击事件
   */
  onMenuTap(e) {
    const type = e.currentTarget.dataset.type
    console.log('点击菜单:', type)

    switch (type) {
      case 'works':
        wx.showToast({
          title: '我的作品功能开发中',
          icon: 'none'
        })
        break
      case 'history':
        wx.showToast({
          title: '处理记录功能开发中',
          icon: 'none'
        })
        break
      case 'settings':
        wx.showToast({
          title: '设置功能开发中',
          icon: 'none'
        })
        break
      case 'about':
        wx.showModal({
          title: '关于我们',
          content: 'AI证件照制作小程序\n版本: 1.0.0\n专注于提供专业的证件照制作服务',
          showCancel: false
        })
        break
      default:
        break
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    console.log('下拉刷新')
    this.loadUserInfo()
    this.loadUserStats()

    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1000)
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'AI证件照制作小程序',
      path: '/pages/index/index',
      imageUrl: '/images/share.jpg'
    }
  }
})
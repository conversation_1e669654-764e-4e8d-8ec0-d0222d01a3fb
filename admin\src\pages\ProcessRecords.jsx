import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Tag, 
  Space, 
  Button, 
  Input, 
  Select, 
  DatePicker,
  Typography,
  Image,
  Tooltip,
  Modal
} from 'antd';
import { 
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  FileImageOutlined
} from '@ant-design/icons';
import { aiAPI } from '../services/api';

const { Title } = Typography;
const { RangePicker } = DatePicker;

const ProcessRecords = () => {
  const [loading, setLoading] = useState(false);
  const [records, setRecords] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [filters, setFilters] = useState({
    userId: '',
    processType: '',
    status: ''
  });
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);

  const loadRecords = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: pageSize,
        ...filters
      };
      
      const response = await aiAPI.getRecords(params);
      if (response.success) {
        setRecords(response.data.records);
        setPagination({
          current: page,
          pageSize,
          total: response.data.pagination.total
        });
      }
    } catch (error) {
      console.error('加载记录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRecords();
  }, [filters]);

  const handleTableChange = (pagination) => {
    loadRecords(pagination.current, pagination.pageSize);
  };

  const handleSearch = () => {
    loadRecords(1, pagination.pageSize);
  };

  const handleReset = () => {
    setFilters({
      userId: '',
      processType: '',
      status: ''
    });
  };

  const showDetail = (record) => {
    setSelectedRecord(record);
    setDetailVisible(true);
  };

  const getStatusColor = (status) => {
    const colors = {
      pending: 'orange',
      processing: 'blue',
      completed: 'green',
      failed: 'red'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      pending: '等待中',
      processing: '处理中',
      completed: '已完成',
      failed: '失败'
    };
    return texts[status] || status;
  };

  const getProcessTypeName = (type) => {
    const names = {
      idPhotoCreation: '证件照制作',
      portraitEnhancement: 'AI人像变清晰',
      imageColorization: '黑白照片上色',
      imageUpscaling: '图片无损放大',
      imageTranslation: '图片翻译',
      portraitClarification: '人像变清晰'
    };
    return names[type] || type;
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: '_id',
      key: '_id',
      width: 100,
      render: (text) => text.slice(-8)
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
      key: 'userId',
      width: 120
    },
    {
      title: '处理类型',
      dataIndex: 'processType',
      key: 'processType',
      width: 150,
      render: (type) => (
        <Tag color="blue">{getProcessTypeName(type)}</Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      )
    },
    {
      title: '原始文件',
      dataIndex: ['originalFile', 'url'],
      key: 'originalFile',
      width: 120,
      render: (url) => url ? (
        <Image
          width={50}
          height={50}
          src={url}
          style={{ objectFit: 'cover' }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ) : (
        <FileImageOutlined style={{ fontSize: 24, color: '#ccc' }} />
      )
    },
    {
      title: '处理时间',
      dataIndex: ['timing', 'duration'],
      key: 'duration',
      width: 100,
      render: (duration) => duration ? `${duration}ms` : '-'
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => showDetail(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>处理记录</Title>
      </div>

      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="用户ID"
            value={filters.userId}
            onChange={(e) => setFilters({ ...filters, userId: e.target.value })}
            style={{ width: 150 }}
          />
          <Select
            placeholder="处理类型"
            value={filters.processType}
            onChange={(value) => setFilters({ ...filters, processType: value })}
            style={{ width: 180 }}
            allowClear
          >
            <Select.Option value="idPhotoCreation">证件照制作</Select.Option>
            <Select.Option value="portraitEnhancement">AI人像变清晰</Select.Option>
            <Select.Option value="imageColorization">黑白照片上色</Select.Option>
            <Select.Option value="imageUpscaling">图片无损放大</Select.Option>
            <Select.Option value="imageTranslation">图片翻译</Select.Option>
            <Select.Option value="portraitClarification">人像变清晰</Select.Option>
          </Select>
          <Select
            placeholder="状态"
            value={filters.status}
            onChange={(value) => setFilters({ ...filters, status: value })}
            style={{ width: 120 }}
            allowClear
          >
            <Select.Option value="pending">等待中</Select.Option>
            <Select.Option value="processing">处理中</Select.Option>
            <Select.Option value="completed">已完成</Select.Option>
            <Select.Option value="failed">失败</Select.Option>
          </Select>
          <Button 
            type="primary" 
            icon={<SearchOutlined />} 
            onClick={handleSearch}
          >
            搜索
          </Button>
          <Button onClick={handleReset}>重置</Button>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={() => loadRecords(pagination.current, pagination.pageSize)}
          >
            刷新
          </Button>
        </Space>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={records}
          rowKey="_id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>

      <Modal
        title="处理记录详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={null}
        width={800}
      >
        {selectedRecord && (
          <div>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div><strong>记录ID:</strong> {selectedRecord._id}</div>
              <div><strong>用户ID:</strong> {selectedRecord.userId}</div>
              <div><strong>处理类型:</strong> {getProcessTypeName(selectedRecord.processType)}</div>
              <div><strong>状态:</strong> 
                <Tag color={getStatusColor(selectedRecord.status)} style={{ marginLeft: 8 }}>
                  {getStatusText(selectedRecord.status)}
                </Tag>
              </div>
              <div><strong>创建时间:</strong> {new Date(selectedRecord.createdAt).toLocaleString()}</div>
              
              {selectedRecord.originalFile?.url && (
                <div>
                  <strong>原始图片:</strong>
                  <div style={{ marginTop: 8 }}>
                    <Image src={selectedRecord.originalFile.url} style={{ maxWidth: 300 }} />
                  </div>
                </div>
              )}
              
              {selectedRecord.processedFile?.url && (
                <div>
                  <strong>处理结果:</strong>
                  <div style={{ marginTop: 8 }}>
                    <Image src={selectedRecord.processedFile.url} style={{ maxWidth: 300 }} />
                  </div>
                </div>
              )}
              
              {selectedRecord.result && (
                <div>
                  <strong>处理结果:</strong>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: 12, 
                    borderRadius: 4,
                    marginTop: 8,
                    whiteSpace: 'pre-wrap'
                  }}>
                    {JSON.stringify(selectedRecord.result, null, 2)}
                  </pre>
                </div>
              )}
            </Space>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ProcessRecords;

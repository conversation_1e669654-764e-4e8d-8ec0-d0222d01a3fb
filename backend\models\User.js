const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  // 微信相关信息
  openid: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  unionid: {
    type: String,
    sparse: true,
    index: true
  },
  
  // 用户基本信息
  nickname: {
    type: String,
    default: '微信用户'
  },
  
  avatar: {
    type: String,
    default: ''
  },
  
  gender: {
    type: Number,
    enum: [0, 1, 2], // 0-未知, 1-男, 2-女
    default: 0
  },
  
  country: {
    type: String,
    default: ''
  },
  
  province: {
    type: String,
    default: ''
  },
  
  city: {
    type: String,
    default: ''
  },
  
  language: {
    type: String,
    default: 'zh_CN'
  },
  
  // 用户状态
  status: {
    type: String,
    enum: ['active', 'inactive', 'banned'],
    default: 'active'
  },
  
  // 登录信息
  lastLoginAt: {
    type: Date,
    default: Date.now
  },
  
  loginCount: {
    type: Number,
    default: 1
  },
  
  // 用户统计
  stats: {
    // 处理次数统计
    processCount: {
      total: { type: Number, default: 0 },
      idPhoto: { type: Number, default: 0 },
      enhancement: { type: Number, default: 0 },
      colorization: { type: Number, default: 0 },
      upscaling: { type: Number, default: 0 },
      translation: { type: Number, default: 0 }
    },
    
    // 作品统计
    workCount: {
      total: { type: Number, default: 0 },
      favorite: { type: Number, default: 0 },
      public: { type: Number, default: 0 }
    },
    
    // 使用统计
    totalDownloads: { type: Number, default: 0 },
    totalShares: { type: Number, default: 0 }
  },
  
  // 用户偏好设置
  preferences: {
    // 默认证件照设置
    defaultPhotoSize: {
      type: String,
      default: '一寸照片'
    },
    
    // 默认背景色
    defaultBackgroundColor: {
      type: String,
      default: '#ffffff'
    },
    
    // 是否开启美颜
    beautyEnabled: {
      type: Boolean,
      default: true
    },
    
    // 美颜级别
    beautyLevel: {
      type: String,
      enum: ['light', 'medium', 'strong'],
      default: 'medium'
    },
    
    // 通知设置
    notifications: {
      processComplete: { type: Boolean, default: true },
      newFeatures: { type: Boolean, default: true }
    }
  },
  
  // 会员信息
  membership: {
    type: {
      type: String,
      enum: ['free', 'premium', 'vip'],
      default: 'free'
    },
    
    expiresAt: {
      type: Date,
      default: null
    },
    
    features: {
      maxProcessPerDay: { type: Number, default: 10 },
      maxFileSize: { type: Number, default: 5 * 1024 * 1024 }, // 5MB
      hdProcessing: { type: Boolean, default: false },
      batchProcessing: { type: Boolean, default: false },
      prioritySupport: { type: Boolean, default: false }
    }
  },
  
  // 设备信息
  deviceInfo: {
    platform: String,
    version: String,
    model: String,
    brand: String,
    system: String,
    lastIP: String
  }
}, {
  timestamps: true
});

// 索引
userSchema.index({ openid: 1 });
userSchema.index({ unionid: 1 });
userSchema.index({ status: 1 });
userSchema.index({ lastLoginAt: -1 });
userSchema.index({ 'membership.type': 1 });

// 静态方法：根据openid查找或创建用户
userSchema.statics.findOrCreateByOpenid = async function(openid, userInfo = {}) {
  let user = await this.findOne({ openid });
  
  if (!user) {
    user = new this({
      openid,
      ...userInfo,
      loginCount: 1,
      lastLoginAt: new Date()
    });
    await user.save();
  } else {
    // 更新登录信息
    user.loginCount += 1;
    user.lastLoginAt = new Date();
    
    // 更新用户信息（如果提供了新信息）
    if (userInfo.nickname) user.nickname = userInfo.nickname;
    if (userInfo.avatar) user.avatar = userInfo.avatar;
    if (userInfo.gender !== undefined) user.gender = userInfo.gender;
    if (userInfo.country) user.country = userInfo.country;
    if (userInfo.province) user.province = userInfo.province;
    if (userInfo.city) user.city = userInfo.city;
    if (userInfo.language) user.language = userInfo.language;
    
    await user.save();
  }
  
  return user;
};

// 实例方法：更新处理统计
userSchema.methods.updateProcessStats = async function(processType) {
  this.stats.processCount.total += 1;
  
  switch (processType) {
    case 'idPhotoCreation':
      this.stats.processCount.idPhoto += 1;
      break;
    case 'portraitEnhancement':
      this.stats.processCount.enhancement += 1;
      break;
    case 'imageColorization':
      this.stats.processCount.colorization += 1;
      break;
    case 'imageUpscaling':
      this.stats.processCount.upscaling += 1;
      break;
    case 'imageTranslation':
      this.stats.processCount.translation += 1;
      break;
  }
  
  return this.save();
};

// 实例方法：更新作品统计
userSchema.methods.updateWorkStats = async function(action, value = 1) {
  switch (action) {
    case 'create':
      this.stats.workCount.total += value;
      break;
    case 'favorite':
      this.stats.workCount.favorite += value;
      break;
    case 'unfavorite':
      this.stats.workCount.favorite -= value;
      break;
    case 'makePublic':
      this.stats.workCount.public += value;
      break;
    case 'makePrivate':
      this.stats.workCount.public -= value;
      break;
    case 'download':
      this.stats.totalDownloads += value;
      break;
    case 'share':
      this.stats.totalShares += value;
      break;
  }
  
  return this.save();
};

// 实例方法：检查会员权限
userSchema.methods.checkMembershipFeature = function(feature) {
  const now = new Date();
  const isExpired = this.membership.expiresAt && this.membership.expiresAt < now;
  
  if (isExpired) {
    this.membership.type = 'free';
  }
  
  return this.membership.features[feature] || false;
};

// 实例方法：获取用户简要信息
userSchema.methods.getPublicInfo = function() {
  return {
    id: this._id,
    nickname: this.nickname,
    avatar: this.avatar,
    membershipType: this.membership.type,
    stats: {
      totalProcess: this.stats.processCount.total,
      totalWorks: this.stats.workCount.total
    },
    joinedAt: this.createdAt
  };
};

module.exports = mongoose.model('User', userSchema);

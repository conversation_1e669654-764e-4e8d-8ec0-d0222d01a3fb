# 服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ai-image-processor

# 阿里云配置
ALIYUN_ACCESS_KEY_ID=your_access_key_id
ALIYUN_ACCESS_KEY_SECRET=your_access_key_secret
ALIYUN_REGION=cn-hangzhou

# 阿里云OSS配置
ALIYUN_OSS_BUCKET=your_oss_bucket
ALIYUN_OSS_REGION=oss-cn-hangzhou
ALIYUN_OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com

# 腾讯云COS配置
TENCENT_SECRET_ID=your_secret_id
TENCENT_SECRET_KEY=your_secret_key
TENCENT_COS_BUCKET=your_cos_bucket
TENCENT_COS_REGION=ap-beijing

# 阿里云视觉AI配置
ALIYUN_VISION_ENDPOINT=https://imagerecog.cn-shanghai.aliyuncs.com

# JWT密钥
JWT_SECRET=your_jwt_secret_key

# 微信小程序配置
WECHAT_APPID=your_wechat_miniprogram_appid
WECHAT_SECRET=your_wechat_miniprogram_secret

# 文件上传配置
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/jpg

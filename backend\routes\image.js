const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const Config = require('../models/Config');
const { uploadToAliyun } = require('../services/aliyunService');
const { uploadToTencent } = require('../services/tencentService');

// 配置multer
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../uploads');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: function (req, file, cb) {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('不支持的文件类型'), false);
    }
  }
});

// 上传图片
router.post('/upload', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: '请选择要上传的图片'
      });
    }

    const config = await Config.getSingleton();
    const file = req.file;
    const filePath = file.path;
    
    let uploadResult = {
      success: false,
      url: '',
      provider: '',
      error: ''
    };

    // 根据配置选择存储提供商
    if (config.system.storageProvider === 'aliyun' || config.system.storageProvider === 'both') {
      try {
        const aliyunResult = await uploadToAliyun(filePath, file.filename, config.aliyun);
        if (aliyunResult.success) {
          uploadResult = {
            success: true,
            url: aliyunResult.url,
            provider: 'aliyun',
            filename: file.filename
          };
        }
      } catch (error) {
        console.error('阿里云上传失败:', error);
      }
    }

    // 如果阿里云失败或配置为腾讯云，尝试腾讯云
    if (!uploadResult.success && (config.system.storageProvider === 'tencent' || config.system.storageProvider === 'both')) {
      try {
        const tencentResult = await uploadToTencent(filePath, file.filename, config.tencent);
        if (tencentResult.success) {
          uploadResult = {
            success: true,
            url: tencentResult.url,
            provider: 'tencent',
            filename: file.filename
          };
        }
      } catch (error) {
        console.error('腾讯云上传失败:', error);
      }
    }

    // 如果云存储都失败，使用本地存储
    if (!uploadResult.success) {
      uploadResult = {
        success: true,
        url: `/uploads/${file.filename}`,
        provider: 'local',
        filename: file.filename
      };
    }

    // 清理本地临时文件（如果使用云存储）
    if (uploadResult.provider !== 'local') {
      try {
        fs.unlinkSync(filePath);
      } catch (error) {
        console.error('清理临时文件失败:', error);
      }
    }

    res.json({
      success: true,
      data: {
        ...uploadResult,
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype
      }
    });

  } catch (error) {
    console.error('上传失败:', error);
    res.status(500).json({
      success: false,
      error: '上传失败',
      message: error.message
    });
  }
});

// 删除图片
router.delete('/:filename', async (req, res) => {
  try {
    const { filename } = req.params;
    const { provider } = req.query;

    if (provider === 'local') {
      const filePath = path.join(__dirname, '../uploads', filename);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    } else if (provider === 'aliyun') {
      // 从阿里云删除
      const config = await Config.getSingleton();
      const { deleteFromAliyun } = require('../services/aliyunService');
      await deleteFromAliyun(filename, config.aliyun);
    } else if (provider === 'tencent') {
      // 从腾讯云删除
      const config = await Config.getSingleton();
      const { deleteFromTencent } = require('../services/tencentService');
      await deleteFromTencent(filename, config.tencent);
    }

    res.json({
      success: true,
      message: '文件删除成功'
    });

  } catch (error) {
    console.error('删除文件失败:', error);
    res.status(500).json({
      success: false,
      error: '删除文件失败',
      message: error.message
    });
  }
});

module.exports = router;

{"name": "cos-nodejs-sdk-v5", "version": "2.15.0", "description": "cos nodejs sdk v5", "main": "index.js", "types": "index.d.ts", "scripts": {"prettier": "prettier --write sdk demo/demo.js test/test.js index.d.ts", "demo": "node demo/demo.js", "demo-sts": "node demo/demo-sts.js", "demo-sts-scope": "node demo/demo-sts-scope.js", "test": "mocha test/test.js", "cov": "istanbul cover _mocha -- -u exports 'test/test.js'", "csp": "mocha test/csp.js"}, "repository": {"type": "git", "url": "git+https://github.com/tencentyun/cos-nodejs-sdk-v5.git"}, "keywords": ["tencent", "tencent cloud", "qcloud", "cos", "cos-sdk"], "author": "carsonxu", "license": "ISC", "bugs": {"url": "https://github.com/tencentyun/cos-nodejs-sdk-v5/issues"}, "homepage": "https://github.com/tencentyun/cos-nodejs-sdk-v5#readme", "dependencies": {"conf": "^9.0.0", "fast-xml-parser": "4.2.5", "mime-types": "^2.1.24", "request": "^2.88.2"}, "devDependencies": {"@types/node": "^14.14.20", "batch": "^0.6.1", "crc64-ecma182.js": "^1.0.0", "mocha": "^4.0.1", "prettier": "^3.0.1", "qcloud-cos-sts": "^3.1.1"}, "engines": {"node": ">= 6"}}
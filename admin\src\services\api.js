import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API请求失败:', error);
    return Promise.reject(error);
  }
);

// 配置相关API
export const configAPI = {
  // 获取配置
  getConfig: () => api.get('/config'),
  
  // 更新阿里云配置
  updateAliyunConfig: (data) => api.put('/config/aliyun', data),
  
  // 更新腾讯云配置
  updateTencentConfig: (data) => api.put('/config/tencent', data),
  
  // 更新AI服务配置
  updateAIServicesConfig: (data) => api.put('/config/ai-services', data),
  
  // 更新系统配置
  updateSystemConfig: (data) => api.put('/config/system', data),
  
  // 测试连接
  testConnection: (provider) => api.post('/config/test', { provider }),

  // 获取美颜设置
  getBeautySettings: () => api.get('/config/beauty-settings'),

  // 更新美颜设置
  updateBeautySettings: (data) => api.put('/config/beauty-settings', data),

  // 获取广告配置
  getAdvertisement: () => api.get('/config/advertisement'),

  // 更新广告配置
  updateAdvertisement: (data) => api.put('/config/advertisement', data),
};

// 图片相关API
export const imageAPI = {
  // 上传图片
  uploadImage: (formData) => {
    return api.post('/image/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },
  
  // 删除图片
  deleteImage: (filename, provider) => {
    return api.delete(`/image/${filename}?provider=${provider}`);
  },
};

// AI处理相关API
export const aiAPI = {
  // 证件照制作
  createIdPhoto: (data) => api.post('/ai/id-photo-creation', data),

  // 人像增强
  enhancePortrait: (data) => api.post('/ai/portrait-enhancement', data),

  // 图片上色
  colorizeImage: (data) => api.post('/ai/image-colorization', data),

  // 图片放大
  upscaleImage: (data) => api.post('/ai/image-upscaling', data),

  // 图片翻译
  translateImage: (data) => api.post('/ai/image-translation', data),

  // 获取处理记录
  getRecords: (params) => api.get('/ai/records', { params }),
};

// 用户认证相关API
export const authAPI = {
  // 微信登录
  wechatLogin: (data) => api.post('/auth/wechat-login', data),

  // 获取用户信息
  getUserInfo: () => api.get('/auth/user-info'),

  // 用户登出
  logout: () => api.post('/auth/logout'),
};

// 证件照尺寸相关API
export const photoSizesAPI = {
  // 获取尺寸列表
  getList: (params) => api.get('/photo-sizes/list', { params }),

  // 搜索尺寸
  search: (params) => api.get('/photo-sizes/search', { params }),

  // 获取单个尺寸详情
  getDetail: (id) => api.get(`/photo-sizes/${id}`),
};

// 用户作品相关API
export const userWorksAPI = {
  // 获取作品列表
  getList: (params) => api.get('/user-works/list', { params }),

  // 保存作品
  save: (data) => api.post('/user-works/save', data),

  // 获取作品详情
  getDetail: (id, params) => api.get(`/user-works/${id}`, { params }),

  // 更新作品
  update: (id, data) => api.put(`/user-works/${id}`, data),

  // 删除作品
  delete: (id, params) => api.delete(`/user-works/${id}`, { params }),

  // 下载作品
  download: (id) => api.post(`/user-works/${id}/download`),

  // 分享作品
  share: (id) => api.post(`/user-works/${id}/share`),

  // 获取用户作品统计
  getStats: (userId) => api.get(`/user-works/stats/${userId}`),
};

// 数据统计相关API
export const statisticsAPI = {
  // 获取探索页面统计
  getExploreCounts: () => api.get('/statistics/explore-counts'),

  // 获取系统概览统计
  getOverview: (params) => api.get('/statistics/overview', { params }),

  // 获取用户活跃度统计
  getUserActivity: (params) => api.get('/statistics/user-activity', { params }),

  // 获取热门功能统计
  getPopularFeatures: (params) => api.get('/statistics/popular-features', { params }),
};

// 系统相关API
export const systemAPI = {
  // 健康检查
  healthCheck: () => api.get('/health'),
};

export default api;

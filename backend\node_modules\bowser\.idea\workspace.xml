<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="e5d5968a-8f20-496e-9038-3ca60f9ade67" name="Default" comment="" />
    <ignored path="bowser.iws" />
    <ignored path=".idea/workspace.xml" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CreatePatchCommitExecutor">
    <option name="PATCH_PATH" value="" />
  </component>
  <component name="FavoritesManager">
    <favorites_list name="bowser" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file leaf-file-name="package.json" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/package.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="34">
              <caret line="2" column="19" selection-start-line="2" selection-start-column="19" selection-end-line="2" selection-end-column="19" />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="bowser.js" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/bowser.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="17">
              <caret line="506" column="5" selection-start-line="506" selection-start-column="5" selection-end-line="506" selection-end-column="5" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>chromium: true</find>
      <find>flag</find>
      <find>msedge</find>
      <find>edge</find>
      <find>Mozilla/5.0 (Linux; Android 8.0; Pixel XL Build/OPP3.170518.006) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.0 Mobile Safari/537.36 EdgA/*********</find>
      <find>version: &quot;&quot;</find>
      <find>Mozilla/5.0 (iPod touch; CPU iPhone OS 9_3_4 like Mac OS X) AppleWebKit/601.1.46 (KHTML, like Gecko) OPiOS/14.0.0.104835 Mobile/13G35 Safari/9537.53</find>
      <find>undefi</find>
      <find>ios</find>
      <find>mobi</find>
      <find>husky</find>
      <find>safari</find>
      <find>w</find>
      <find>x</find>
      <find>tablet</find>
      <find>we</find>
      <find>webos</find>
      <find>parse</find>
      <find>parseBrowser</find>
      <find>@priva</find>
      <find>parsedResult</find>
      <find>Parser</find>
      <find>spy</find>
      <find>ge</find>
      <find>parseEngine</find>
      <find>engin</find>
      <find>ava</find>
      <find>getWindowsVersionName</find>
      <find>android</find>
      <find>_parsed</find>
    </findStrings>
    <replaceStrings>
      <replace>descript</replace>
      <replace>describe</replace>
      <replace>(\d+(\.?_?\d+)+)</replace>
    </replaceStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/.editorconfig" />
        <option value="$PROJECT_DIR$/src/copyright.js" />
        <option value="$PROJECT_DIR$/make/build.js" />
        <option value="$PROJECT_DIR$/ISSUE_TEMPLATE.md" />
        <option value="$PROJECT_DIR$/.testem.json" />
        <option value="$PROJECT_DIR$/transpile-json-to-yaml.js" />
        <option value="$PROJECT_DIR$/src/parser-enignes.js" />
        <option value="$PROJECT_DIR$/src/parser-engines.js" />
        <option value="$PROJECT_DIR$/convert-old-json-to-yaml.js" />
        <option value="$PROJECT_DIR$/src/parsed-object.js" />
        <option value="$PROJECT_DIR$/useragentstrings.yml" />
        <option value="$PROJECT_DIR$/helpers/convert-old-json-to-yaml.js" />
        <option value="$PROJECT_DIR$/test/test.js" />
        <option value="$PROJECT_DIR$/README.md" />
        <option value="$PROJECT_DIR$/test/acceptance/useragentstrings.yml" />
        <option value="$PROJECT_DIR$/src/parser-platforms.js" />
        <option value="$PROJECT_DIR$/.eslintignore" />
        <option value="$PROJECT_DIR$/.eslintrc.yml" />
        <option value="$PROJECT_DIR$/src/parser-browsers.js" />
        <option value="$PROJECT_DIR$/src/new-bowser.js" />
        <option value="$PROJECT_DIR$/typings.d.ts" />
        <option value="$PROJECT_DIR$/test/acceptance/test-list-of-ua.js" />
        <option value="$PROJECT_DIR$/bower.json" />
        <option value="$PROJECT_DIR$/src/useragents.js" />
        <option value="$PROJECT_DIR$/CHANGELOG.md" />
        <option value="$PROJECT_DIR$/test/unit/parser.js" />
        <option value="$PROJECT_DIR$/test/unit/bowser.js" />
        <option value="$PROJECT_DIR$/.coveralls.yml" />
        <option value="$PROJECT_DIR$/.travis.yml" />
        <option value="$PROJECT_DIR$/.npmignore" />
        <option value="$PROJECT_DIR$/.gitignore" />
        <option value="$PROJECT_DIR$/package.json" />
        <option value="$PROJECT_DIR$/src/bowser.js" />
        <option value="$PROJECT_DIR$/.nycrc" />
        <option value="$PROJECT_DIR$/.babelrc" />
        <option value="$PROJECT_DIR$/src/utils.js" />
        <option value="$PROJECT_DIR$/src/parser-os.js" />
        <option value="$PROJECT_DIR$/test/unit/utils.js" />
        <option value="$PROJECT_DIR$/src/parser.js" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER">
    <package-json value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="JsFlowSettings">
    <service-enabled>true</service-enabled>
    <exe-path />
    <other-services-enabled>true</other-services-enabled>
    <auto-save>true</auto-save>
  </component>
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="NodeModulesDirectoryManager">
    <handled-path value="$PROJECT_DIR$/node_modules" />
  </component>
  <component name="NodePackageJsonFileManager">
    <packageJsonPaths>
      <path value="$PROJECT_DIR$/package.json" />
    </packageJsonPaths>
  </component>
  <component name="ProjectFrameBounds">
    <option name="y" value="22" />
    <option name="width" value="1920" />
    <option name="height" value="1058" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <autoscrollFromSource ProjectPane="true" />
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="bowser" type="b2602c69:ProjectViewProjectNode" />
              <item name="bowser" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="bowser" type="b2602c69:ProjectViewProjectNode" />
              <item name="bowser" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="HbShouldOpenHtmlAsHb" value="" />
    <property name="JavaScriptWeakerCompletionTypeGuess" value="true" />
    <property name="SearchEverywhereHistoryKey" value="plat&#9;FILE&#9;file:///Users/<USER>/Projects/github/bowser/src/parser-platforms.js" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="editor.config.accepted" value="true" />
    <property name="javascript.nodejs.core.library.configured.version" value="4.4.7" />
    <property name="js-jscs-nodeInterpreter" value="$USER_HOME$/.nvm/versions/node/v4.2.4/bin/node" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.detected.package.standard" value="true" />
    <property name="node.js.path.for.package.eslint" value="node" />
    <property name="node.js.path.for.package.standard" value="node" />
    <property name="node.js.selected.package.eslint" value="$PROJECT_DIR$/node_modules/eslint" />
    <property name="node.js.selected.package.standard" value="$PROJECT_DIR$/node_modules/eslint" />
    <property name="nodejs.mocha.mocha_node_package_dir" value="$PROJECT_DIR$/node_modules/mocha" />
    <property name="nodejs_interpreter_path" value="node" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="settings.editor.selected.configurable" value="coverage" />
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/test/acceptance" />
      <recent name="$PROJECT_DIR$/helpers" />
      <recent name="$PROJECT_DIR$" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager" selected="Node.js.watch tests">
    <configuration name="Unnamed" type="ChromiumRemoteDebugType" factoryName="Chromium Remote" port="9229" isV8Legacy="true" />
    <configuration name="watch tests" type="NodeJSConfigurationType" factoryName="Node.js" application-parameters="--verbose" path-to-node="/usr/local/Cellar/node/7.8.0/bin/node" path-to-js-file="node_modules/.bin/ava" working-dir="$PROJECT_DIR$">
      <EXTENSION ID="com.jetbrains.nodejs.run.NodeJSProfilingRunConfigurationExtension">
        <profiling v8-profiler-path="$USER_HOME$/.nvm/versions/node/v4.4.7/lib/node_modules/v8-profiler" />
      </EXTENSION>
    </configuration>
    <configuration default="true" type="js.build_tools.gulp" factoryName="Gulp.js">
      <node-interpreter>project</node-interpreter>
      <node-options />
      <gulpfile />
      <tasks />
      <arguments />
      <envs />
    </configuration>
    <configuration name="test" type="js.build_tools.npm" factoryName="npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="test" />
      </scripts>
      <arguments value="--inspect-brk" />
      <node-interpreter value="/usr/local/Cellar/node/7.8.0/bin/node" />
      <envs />
    </configuration>
    <configuration default="true" type="mocha-javascript-test-runner" factoryName="Mocha">
      <node-interpreter>$USER_HOME$/.nvm/versions/node/v4.2.4/bin/node</node-interpreter>
      <node-options />
      <working-directory>$PROJECT_DIR$</working-directory>
      <pass-parent-env>true</pass-parent-env>
      <ui>bdd</ui>
      <extra-mocha-options />
      <test-kind>DIRECTORY</test-kind>
      <test-directory />
      <recursive>false</recursive>
    </configuration>
    <list>
      <item itemvalue="Attach to Node.js/Chrome.Unnamed" />
      <item itemvalue="Node.js.watch tests" />
      <item itemvalue="npm.test" />
    </list>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e5d5968a-8f20-496e-9038-3ca60f9ade67" name="Default" comment="" />
      <created>1460663203148</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1460663203148</updated>
      <workItem from="1467903205339" duration="2348000" />
      <workItem from="1469530933856" duration="577000" />
      <workItem from="1469613736440" duration="843000" />
      <workItem from="1470856356825" duration="1760000" />
      <workItem from="1471694116386" duration="3831000" />
      <workItem from="1472504254866" duration="2038000" />
      <workItem from="1472506362075" duration="69000" />
      <workItem from="1472594666841" duration="2699000" />
      <workItem from="1472640870661" duration="1938000" />
      <workItem from="1472806795867" duration="910000" />
      <workItem from="1474185796367" duration="13000" />
      <workItem from="1474188003327" duration="3018000" />
      <workItem from="1474314087540" duration="1938000" />
      <workItem from="1474359763987" duration="11000" />
      <workItem from="1474916352416" duration="284000" />
      <workItem from="1475653755459" duration="30000" />
      <workItem from="1477903203162" duration="2196000" />
      <workItem from="1478186752996" duration="320000" />
      <workItem from="1478187077648" duration="1126000" />
      <workItem from="1478298430363" duration="2567000" />
      <workItem from="1480969963547" duration="957000" />
      <workItem from="1482500551547" duration="211000" />
      <workItem from="1482503774910" duration="735000" />
      <workItem from="1483826622613" duration="503000" />
      <workItem from="1485119874288" duration="3226000" />
      <workItem from="1490357099078" duration="447000" />
      <workItem from="1490993891079" duration="6576000" />
      <workItem from="1491136107895" duration="7722000" />
      <workItem from="1491239706765" duration="714000" />
      <workItem from="1491249803179" duration="1984000" />
      <workItem from="1491336207678" duration="1618000" />
      <workItem from="1491674896468" duration="1676000" />
      <workItem from="1491725583169" duration="20809000" />
      <workItem from="1491825357873" duration="935000" />
      <workItem from="1491858641932" duration="606000" />
      <workItem from="1491933288606" duration="2000" />
      <workItem from="1491935322145" duration="643000" />
      <workItem from="1491936995311" duration="2957000" />
      <workItem from="1492110236327" duration="1850000" />
      <workItem from="1492272289300" duration="985000" />
      <workItem from="1492276168333" duration="10503000" />
      <workItem from="1493559521213" duration="367000" />
      <workItem from="1493573087749" duration="12000" />
      <workItem from="1493573143412" duration="671000" />
      <workItem from="1493712648190" duration="26000" />
      <workItem from="1493983048502" duration="1970000" />
      <workItem from="1495133393210" duration="7807000" />
      <workItem from="1495175490141" duration="467000" />
      <workItem from="1496943656498" duration="13955000" />
      <workItem from="1497033788132" duration="7107000" />
      <workItem from="1497164175401" duration="1361000" />
      <workItem from="1497703951161" duration="3005000" />
      <workItem from="1497790692911" duration="606000" />
      <workItem from="1498335998463" duration="1814000" />
      <workItem from="1499200847143" duration="5483000" />
      <workItem from="1499934318349" duration="520000" />
      <workItem from="1502996571785" duration="7286000" />
      <workItem from="1503159696902" duration="5078000" />
      <workItem from="1503238622588" duration="190000" />
      <workItem from="1503238828060" duration="6025000" />
      <workItem from="1503300520953" duration="12000" />
      <workItem from="1504072300165" duration="742000" />
      <workItem from="1506347153317" duration="355000" />
      <workItem from="1507364593075" duration="2743000" />
      <workItem from="1508265469020" duration="6002000" />
      <workItem from="1508501174027" duration="352000" />
      <workItem from="1509979153403" duration="605000" />
      <workItem from="1513332788354" duration="2020000" />
      <workItem from="1513798033267" duration="6864000" />
      <workItem from="1513956669871" duration="1376000" />
      <workItem from="1514488057505" duration="1107000" />
      <workItem from="1514882737984" duration="222000" />
      <workItem from="1516101669528" duration="38000" />
      <workItem from="1517854586116" duration="940000" />
      <workItem from="1517908003086" duration="91000" />
      <workItem from="1520884638839" duration="1634000" />
      <workItem from="1520949116701" duration="1000" />
      <workItem from="1530113213082" duration="532000" />
      <workItem from="1530126970711" duration="4447000" />
      <workItem from="1530206920060" duration="5405000" />
      <workItem from="1530262456167" duration="106000" />
      <workItem from="1530352808472" duration="13988000" />
      <workItem from="1530395750315" duration="7885000" />
      <workItem from="1530539643434" duration="396000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="215788000" />
  </component>
  <component name="TodoView">
    <todo-panel id="selected-file">
      <is-autoscroll-to-source value="true" />
    </todo-panel>
    <todo-panel id="all">
      <are-packages-shown value="true" />
      <is-autoscroll-to-source value="true" />
    </todo-panel>
  </component>
  <component name="ToolWindowManager">
    <frame x="0" y="22" width="1920" height="1058" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info active="true" content_ui="combo" id="Project" order="0" sideWeight="0.4957265" visible="true" weight="0.16482359" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Docker" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Event Log" order="7" side_tool="true" />
      <window_info anchor="bottom" id="Run" order="2" weight="0.353562" />
      <window_info anchor="bottom" id="Version Control" order="7" weight="0.32849604" />
      <window_info id="npm" order="2" side_tool="true" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="bottom" id="Terminal" order="7" weight="0.26923078" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.23412699" />
      <window_info id="Favorites" order="2" sideWeight="0.50427353" side_tool="true" weight="0.1790416" />
      <window_info anchor="bottom" id="TypeScript" order="7" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Find" order="1" />
    </layout>
    <layout-to-restore>
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="bottom" id="Docker" order="7" show_stripe_button="false" />
      <window_info id="npm" order="2" side_tool="true" />
      <window_info anchor="bottom" id="Run" order="2" weight="0.353562" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info active="true" content_ui="combo" id="Project" order="0" sideWeight="0.4957265" visible="true" weight="0.22057787" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="TypeScript" order="8" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.23412699" />
      <window_info anchor="bottom" id="Terminal" order="9" weight="0.26923078" />
      <window_info anchor="bottom" id="Event Log" order="10" side_tool="true" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="Version Control" order="11" weight="0.32849604" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info id="Favorites" order="3" sideWeight="0.50427353" side_tool="true" weight="0.1790416" />
    </layout-to-restore>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="Vcs.Log.UiProperties">
    <option name="RECENTLY_FILTERED_USER_GROUPS">
      <collection />
    </option>
    <option name="RECENTLY_FILTERED_BRANCH_GROUPS">
      <collection />
    </option>
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="2678400000" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <option name="time" value="2" />
    </breakpoint-manager>
    <watches-manager>
      <configuration name="NodeJSConfigurationType">
        <watch expression="browsersList" language="JavaScript" />
      </configuration>
    </watches-manager>
  </component>
  <component name="editorHistoryManager">
    <entry file="jar://$APPLICATION_HOME_DIR$/plugins/JavaScriptLanguage/lib/JavaScriptLanguage.jar!/com/intellij/lang/javascript/index/predefined/HTML5.js" />
    <entry file="file://$PROJECT_DIR$/node_modules/smoosh/lib/smoosh/index.js" />
    <entry file="file://$PROJECT_DIR$/node_modules/smoosh/index.js" />
    <entry file="file://$PROJECT_DIR$/make/build.js" />
    <entry file="file://$PROJECT_DIR$/node_modules/sinon/lib/sinon.js" />
    <entry file="file://$PROJECT_DIR$/LICENSE">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="45">
          <caret line="3" column="11" lean-forward="true" selection-start-line="3" selection-start-column="11" selection-end-line="3" selection-end-column="11" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/parser.js" />
    <entry file="file://$PROJECT_DIR$/lib/parser-os.js" />
    <entry file="file://$PROJECT_DIR$/ISSUE_TEMPLATE.md" />
    <entry file="file://$PROJECT_DIR$/src/parsed-object.js" />
    <entry file="file://$PROJECT_DIR$/.testem.json" />
    <entry file="file://$PROJECT_DIR$/node_modules/yaml2json/index.js" />
    <entry file="file://$PROJECT_DIR$/node_modules/yamljs/lib/Yaml.js" />
    <entry file="file://$PROJECT_DIR$/Makefile">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="15" column="19" lean-forward="true" selection-start-line="15" selection-start-column="19" selection-end-line="15" selection-end-column="19" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/helpers/convert-old-json-to-yaml.js" />
    <entry file="file://$PROJECT_DIR$/src/old.bowser.js" />
    <entry file="file://$PROJECT_DIR$/.eslintignore" />
    <entry file="file://$PROJECT_DIR$/typings.d.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="68">
          <caret line="4" column="9" lean-forward="true" selection-start-line="4" selection-start-column="9" selection-end-line="4" selection-end-column="9" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.eslintrc.yml" />
    <entry file="file://$PROJECT_DIR$/src/bowser.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="170">
          <caret line="10" column="15" lean-forward="true" selection-start-line="10" selection-start-column="15" selection-end-line="10" selection-end-column="15" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/bowser.js" />
    <entry file="file://$PROJECT_DIR$/bowser.min.js" />
    <entry file="file://$PROJECT_DIR$/test.old.js" />
    <entry file="file://$PROJECT_DIR$/test/acceptance/test-list-of-ua.js" />
    <entry file="file://$PROJECT_DIR$/bower.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="15" column="14" lean-forward="true" selection-start-line="15" selection-start-column="14" selection-end-line="15" selection-end-column="14" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/test.js" />
    <entry file="file://$PROJECT_DIR$/CHANGELOG.md">
      <provider editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="15" column="23" lean-forward="true" selection-start-line="15" selection-start-column="23" selection-end-line="15" selection-end-column="23" />
        </state>
      </provider>
      <provider selected="true" editor-type-id="split-provider[text-editor;MarkdownPreviewEditor]">
        <state split_layout="FIRST">
          <first_editor relative-caret-position="646">
            <caret line="38" selection-start-line="38" selection-end-line="38" />
          </first_editor>
          <second_editor>
            <markdownNavigatorState />
          </second_editor>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/useragents.js" />
    <entry file="file://$APPLICATION_HOME_DIR$/plugins/JavaScriptLanguage/jsLanguageServicesImpl/external/lib.es5.d.ts">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="244">
          <caret line="890" column="4" selection-start-line="890" selection-start-column="4" selection-end-line="890" selection-end-column="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/README.md">
      <provider editor-type-id="text-editor">
        <state relative-caret-position="810">
          <caret line="54" column="43" selection-start-line="54" selection-start-column="43" selection-end-line="54" selection-end-column="43" />
        </state>
      </provider>
      <provider selected="true" editor-type-id="split-provider[text-editor;MarkdownPreviewEditor]">
        <state split_layout="FIRST">
          <first_editor relative-caret-position="-2024">
            <caret line="15" column="3" selection-start-line="15" selection-start-column="3" selection-end-line="15" selection-end-column="3" />
          </first_editor>
          <second_editor>
            <markdownNavigatorState />
          </second_editor>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.coveralls.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state>
          <caret column="45" selection-start-column="45" selection-end-column="45" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.travis.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="68">
          <caret line="4" lean-forward="true" selection-start-line="4" selection-end-line="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/coverage/bowser.js.html">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="127">
          <caret line="225" column="6" lean-forward="true" selection-start-line="225" selection-start-column="6" selection-end-line="225" selection-end-column="6" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.gitignore">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="102">
          <caret line="6" column="8" selection-start-line="6" selection-start-column="8" selection-end-line="6" selection-end-column="8" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.npmignore">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="68">
          <caret line="4" selection-start-line="4" selection-end-line="4" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/acceptance/useragentstrings.yml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="85">
          <caret line="5" column="17" lean-forward="true" selection-start-line="5" selection-start-column="17" selection-end-line="5" selection-end-column="17" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.nycrc">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="34">
          <caret line="2" column="21" selection-start-line="2" selection-start-column="21" selection-end-line="2" selection-end-column="21" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/.babelrc">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="119">
          <caret line="7" column="3" selection-start-line="7" selection-start-column="3" selection-end-line="7" selection-end-column="3" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/coverage/index.html">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/src/utils.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="221">
          <caret line="13" column="28" selection-start-line="13" selection-start-column="28" selection-end-line="13" selection-end-column="28" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/unit/utils.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="51">
          <caret line="3" column="24" lean-forward="true" selection-start-line="3" selection-start-column="24" selection-end-line="3" selection-end-column="24" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/unit/parser.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-139">
          <caret line="14" lean-forward="true" selection-start-line="14" selection-end-line="14" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/parser-engines.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1411">
          <caret line="83" column="16" selection-start-line="83" selection-start-column="16" selection-end-line="83" selection-end-column="16" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/parser-platforms.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-1346">
          <caret line="18" column="14" lean-forward="true" selection-start-line="18" selection-start-column="14" selection-end-line="18" selection-end-column="14" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/parser-os.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="85">
          <caret line="5" column="16" lean-forward="true" selection-start-line="5" selection-start-column="16" selection-end-line="5" selection-end-column="16" />
          <folding>
            <element signature="e#0#68#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/test/unit/bowser.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="15" column="29" selection-start-line="15" selection-start-column="29" selection-end-line="15" selection-end-column="29" />
          <folding>
            <element signature="e#0#23#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/parser-browsers.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="714">
          <caret line="65" column="4" lean-forward="true" selection-start-line="65" selection-start-column="4" selection-end-line="65" selection-end-column="4" />
          <folding>
            <element signature="e#1322#1383#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/parser.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="687">
          <caret line="312" column="17" lean-forward="true" selection-start-line="312" selection-start-column="17" selection-end-line="312" selection-end-column="17" />
          <folding>
            <element signature="e#0#28#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package-lock.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="15" column="40" selection-start-line="15" selection-start-column="40" selection-end-line="15" selection-end-column="40" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/bowser.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="17">
          <caret line="506" column="5" selection-start-line="506" selection-start-column="5" selection-end-line="506" selection-end-column="5" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="34">
          <caret line="2" column="19" selection-start-line="2" selection-start-column="19" selection-end-line="2" selection-end-column="19" />
        </state>
      </provider>
    </entry>
  </component>
</project>
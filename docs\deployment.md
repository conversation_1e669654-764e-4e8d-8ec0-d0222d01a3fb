# 部署指南

## 环境要求

### 后端服务
- Node.js 16.x 或更高版本
- MongoDB 4.4 或更高版本
- npm 或 yarn 包管理器

### 管理后台
- Node.js 16.x 或更高版本
- 现代浏览器支持

### 小程序端
- 微信开发者工具
- 微信小程序开发者账号

## 部署步骤

### 1. 后端服务部署

#### 1.1 安装依赖
```bash
cd backend
npm install
```

#### 1.2 配置环境变量
复制 `.env.example` 为 `.env` 并配置：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下参数：
- `MONGODB_URI`: MongoDB连接字符串
- `ALIYUN_ACCESS_KEY_ID`: 阿里云AccessKey ID
- `ALIYUN_ACCESS_KEY_SECRET`: 阿里云AccessKey Secret
- `TENCENT_SECRET_ID`: 腾讯云Secret ID
- `TENCENT_SECRET_KEY`: 腾讯云Secret Key

#### 1.3 启动服务
```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

### 2. 管理后台部署

#### 2.1 安装依赖
```bash
cd admin
npm install
```

#### 2.2 配置API地址
修改 `src/services/api.js` 中的 `baseURL` 为实际的后端服务地址。

#### 2.3 构建和部署
```bash
# 构建生产版本
npm run build

# 部署到服务器
# 将 dist 目录内容上传到 Web 服务器
```

### 3. 小程序端部署

#### 3.1 配置小程序信息
1. 在微信公众平台注册小程序
2. 获取小程序 AppID
3. 修改 `miniprogram/project.config.json` 中的 `appid`

#### 3.2 配置服务器域名
在微信公众平台配置以下域名：
- request合法域名：添加后端服务域名
- uploadFile合法域名：添加后端服务域名

#### 3.3 修改API地址
修改 `miniprogram/app.js` 中的 `apiBaseUrl` 为实际的后端服务地址。

#### 3.4 上传代码
1. 使用微信开发者工具打开 `miniprogram` 目录
2. 点击"上传"按钮上传代码
3. 在微信公众平台提交审核

## 云服务配置

### 阿里云配置

#### 1. 开通服务
- 对象存储 OSS
- 视觉智能开放平台

#### 2. 创建AccessKey
1. 登录阿里云控制台
2. 访问 RAM 访问控制
3. 创建用户并生成AccessKey

#### 3. 配置权限
为用户添加以下权限：
- AliyunOSSFullAccess
- AliyunVisionIntelligenceFullAccess

### 腾讯云配置

#### 1. 开通服务
- 对象存储 COS

#### 2. 创建密钥
1. 登录腾讯云控制台
2. 访问 CAM 访问管理
3. 创建用户并生成密钥

#### 3. 配置权限
为用户添加以下权限：
- QcloudCOSFullAccess

## 安全配置

### 1. 网络安全
- 使用 HTTPS 协议
- 配置防火墙规则
- 限制API访问频率

### 2. 数据安全
- 定期备份数据库
- 加密敏感配置信息
- 设置访问权限控制

### 3. 监控告警
- 配置服务监控
- 设置异常告警
- 记录操作日志

## 性能优化

### 1. 后端优化
- 使用连接池
- 配置缓存策略
- 优化数据库查询

### 2. 前端优化
- 启用 Gzip 压缩
- 配置 CDN 加速
- 优化图片资源

### 3. 小程序优化
- 分包加载
- 图片懒加载
- 减少包体积

## 故障排查

### 常见问题

#### 1. 后端服务无法启动
- 检查 Node.js 版本
- 检查 MongoDB 连接
- 检查端口占用情况

#### 2. API 调用失败
- 检查网络连接
- 验证 API 密钥配置
- 查看服务器日志

#### 3. 小程序无法上传
- 检查域名配置
- 验证 AppID 设置
- 检查网络权限

### 日志查看
```bash
# 查看后端日志
pm2 logs

# 查看 Nginx 日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

## 维护更新

### 1. 版本更新
- 备份当前版本
- 测试新版本功能
- 灰度发布更新

### 2. 数据备份
- 定期备份数据库
- 备份配置文件
- 测试恢复流程

### 3. 监控维护
- 监控服务状态
- 检查资源使用
- 优化性能瓶颈
